"""
主程序入口，提供命令行接口
"""
import argparse
import os
import sqlite3
import sys
import warnings
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import dateutil.relativedelta

# 抑制urllib3警告
warnings.filterwarnings('ignore', category=UserWarning, module='urllib3')
warnings.filterwarnings('ignore', message='.*urllib3 v2 only supports OpenSSL.*')
import urllib3
urllib3.disable_warnings()

# 导入配置管理器
from config.config import config_manager

# 导入数据模块
from data.fetcher import DataFetcher, DataCache, DataProcessor
from data.data_cleaner import DataCleaner

# 导入因子模块
from factor.factor_calculator_factory import (
    create_default_calculator,
    create_double_low_calculator,
    create_advanced_calculator,
    create_enhanced_calculator,
    create_calculator_from_args
)

# 导入策略模块
from strategy.topndropoutk import TopNDropoutKStrategy, SignalGenerator, EventHandler, ParameterOptimizer

# 导入回测模块
from backtest.backtester import Backtester, TradingSimulator, PerformanceAnalyzer, ReportGenerator


# 导入实时模拟交易模块
from realtime.simulator import RealtimeSimulator

# 导入筛选模块
from filter.filter_factory import create_filter_from_args

# 导入日志模块
from custom_logging.logger import Logger, ErrorHandler, DocumentGenerator

# 初始化日志
logger = Logger(name='main')
error_handler = ErrorHandler()


def parse_args():
    """
    解析命令行参数
    
    Returns:
        解析后的参数
    """
    parser = argparse.ArgumentParser(description='可转债量化投资工具')
    
    # 运行模式
    parser.add_argument('--mode', type=str, choices=['data', 'factor', 'backtest', 'realtime', 'optimize', 'status', 'data_check', 'linear_model', 'ml_model', 'ft_transformer', 'incremental_factor', 'ic', 'flow_backtest'],
                        help='运行模式')
    
    # IC计算参数
    parser.add_argument('--ic', action='store_true', help='计算因子IC值')
    
    # 数据操作
    parser.add_argument('--action', type=str, choices=['fetch', 'update', 'update_close', 'fetch_stock', 'fetch_call', 'full_price'],
                        help='数据操作')
    
    # 日期参数
    parser.add_argument('--start_date', '--start-date', type=str, help='开始日期，格式YYYY-MM-DD')
    parser.add_argument('--end_date', '--end-date', type=str, help='结束日期，格式YYYY-MM-DD')
    parser.add_argument('--date', type=str, help='单一日期，格式YYYY-MM-DD')

    # 可转债代码参数（用于factor模式的单债券更新）
    parser.add_argument('--code', type=str, help='可转债代码，格式如110059.SH（仅用于factor模式，指定后只更新该债券的因子）')

    # 正股代码参数（用于data模式的fetch_stock操作）
    parser.add_argument('--ts_code', type=str, help='正股代码，格式如000001.SZ（用于data模式的fetch_stock操作）')
    
    # 强制更新参数
    parser.add_argument('--force', action='store_true', help='强制更新所有记录（用于full_price操作）')

    # 流式回测专用参数
    parser.add_argument('--train_start_date', '--train-start-date', type=str, help='训练期开始日期，格式YYYY-MM-DD（用于flow_backtest模式）')
    
    # 策略参数
    parser.add_argument('--strategy', type=str, help='策略名称')
    parser.add_argument('--params', type=str, help='策略参数，格式"n=10,k=2"')
    parser.add_argument('--param_range', type=str, help='参数优化范围，格式"n=5:15:1,k=1:5:1"')

    # 筛选参数
    parser.add_argument('--filter', type=str, choices=['double_low', 'none', 'maturity'],
                       default='maturity', help='预筛选策略：double_low(双低策略+到期筛选), none(无筛选), maturity(仅到期筛选)，默认到期筛选')
    
    # 计算精度参数
    parser.add_argument('--precision', type=str, choices=['fast', 'medium', 'high', 'full'],
                        default='medium', help='计算精度等级: fast(快速), medium(中等), high(高精度), full(完整精度)')

    # 因子计算器参数
    parser.add_argument('--calculator', type=str, choices=['default', 'double_low', 'advanced', 'enhanced', 'zl_only', 'momentum_only', 'technical_only', 'basic_price'],
                       default='default', help='因子计算器类型: default(默认组合), double_low(双低组合), advanced(高级组合), enhanced(增强因子), zl_only(仅ZL模型), momentum_only(仅动量), technical_only(仅技术因子)')
    parser.add_argument('--alpha', type=float, default=0.5, help='双低因子权重参数，范围[0,1]，默认0.5')

    # 线性回归模型参数
    parser.add_argument('--model-name', type=str, default='default',
                        help='线性回归模型名称')
    parser.add_argument('--use-linear-model', action='store_true',
                        help='使用线性回归模型进行因子组合')
    parser.add_argument('--train-model', action='store_true',
                        help='训练新的线性回归模型')
    parser.add_argument('--factor-config', type=str, default='default',
                        help='因子配置: default(默认因子组合), best_ten(IC排名前十因子)')
    parser.add_argument('--regularization', type=str, default='ridge',
                        choices=['none', 'ridge', 'lasso'],
                        help='正则化方法')
    parser.add_argument('--reg-alpha', type=float, default=1.0,
                        help='正则化强度')
    parser.add_argument('--use_sample_weights', action='store_true',
                        help='使用样本权重训练，基于每日收益率排名计算权重')
    parser.add_argument('--sorted_feature', action='store_true', 
                        help='使用排序特征：将每个特征转换为时间维度(60天)和横截面维度的排序分位数')
    parser.add_argument('--target-factor', type=str, default='next_return',
                        help='目标因子名称')
    parser.add_argument('--return-days', type=int, default=1,
                        help='收益率计算天数，默认1天（隔天收益率）')
    parser.add_argument('--custom-factors', type=str, nargs='*',
                        help='自定义因子列表')
    parser.add_argument('--list-models', action='store_true',
                        help='列出所有可用的线性回归模型')
    
    # AutoGluon/ML模型参数
    parser.add_argument('--ml-model-name', type=str, default='default_ml',
                        help='AutoGluon模型名称')
    parser.add_argument('--target-days', '--target_days', type=int, default=1,
                        help='预测目标天数（未来n天收益率）')
    parser.add_argument('--time-limit', type=int, default=300,
                        help='AutoGluon训练时间限制（秒）')
    parser.add_argument('--presets', type=str, default='medium_quality',
                        choices=['medium_quality', 'high_quality', 'best_quality'],
                        help='AutoGluon模型预设质量')
    parser.add_argument('--train-ml-model', action='store_true',
                        help='训练新的AutoGluon模型')
    parser.add_argument('--use-ml-model', action='store_true',
                        help='使用AutoGluon模型进行预测')
    parser.add_argument('--list-ml-models', action='store_true',
                        help='列出所有可用的AutoGluon模型')
    parser.add_argument('--preprocessing-config', type=str, default='no_imputation',
                        choices=['default', 'conservative', 'aggressive', 'time_series', 'fast_training', 'no_imputation'],
                        help='特征预处理配置: default(默认+特征工程), conservative(禁用特征工程), aggressive(激进), time_series(时序), fast_training(快速), no_imputation(无填充+特征工程)')
    
    # FT-Transformer模型参数
    parser.add_argument('--use-ft-model', action='store_true',
                        help='使用FT-Transformer模型进行回测')
    parser.add_argument('--ft-model-name', type=str, default='default_ft',
                        help='FT-Transformer模型名称')
    
    # 增量因子计算参数
    parser.add_argument('--incremental', action='store_true',
                        help='启用增量因子计算（只计算新因子）')
    parser.add_argument('--force-update', action='store_true',
                        help='强制更新所有因子（配合增量模式使用）')
    
    # 多线程计算参数
    parser.add_argument('--multithreaded', action='store_true',
                        help='启用多线程因子计算（大幅提升计算速度）')
    parser.add_argument('--max-workers', '--threads', type=int, default=None,
                        help='最大线程数，默认为CPU核心数+4')
    parser.add_argument('--chunk-size', type=int, default=30,
                        help='每个线程处理的天数，默认30天')
    
    # 因子过滤参数
    parser.add_argument('--factor-filter', type=str, 
                        choices=['enhanced_only', 'basic_only', 'technical_only', 'momentum_only', 'zl_only', 'double_low_only'],
                        help='因子类型过滤器: enhanced_only(仅增强因子), basic_only(仅基础因子), technical_only(仅技术因子), momentum_only(仅动量因子), zl_only(仅ZL因子), double_low_only(仅双低因子)')
    parser.add_argument('--enhanced_only', action='store_true',
                        help='仅计算增强因子（等同于--factor-filter enhanced_only）')

    # 流式回测参数
    parser.add_argument('--model_prefix', type=str, default='flow_model',
                        help='流式回测中模型名称前缀，默认为flow_model')
    parser.add_argument('--train_interval', type=str, default='3M',
                        help='训练间隔，支持格式：1M(1个月), 3M(3个月), 6M(6个月), 1Y(1年)，默认3M')

    # 回测参数（已集成最优配置）
    parser.add_argument('--rebalance_frequency', type=str, choices=['daily', 'weekly', 'monthly', 'quarterly'],
                       default='daily', help='调整频率：daily(日度), weekly(周度), monthly(月度) 或 quarterly(季度)，默认日度调整')
    parser.add_argument('--transaction_cost', type=float, default=0.002, help='交易成本费率，默认0.2%%')
    parser.add_argument('--factor_ascending', action='store_true', help='因子升序排列（默认降序，即因子值高的优先）')
    parser.add_argument('--factor_name', type=str, default='combined_factor',
                       help='指定回测使用的因子名称，默认为combined_factor（复合因子），可选double_low_score（双低得分）')
    
    args = parser.parse_args()
    
    # 处理参数别名和兼容性
    if args.enhanced_only:
        args.factor_filter = 'enhanced_only'
    
    return args


def parse_params(params_str):
    """
    解析策略参数
    
    Args:
        params_str: 参数字符串，格式"n=10,k=2"
    
    Returns:
        参数字典
    """
    if not params_str:
        return {}
    
    params = {}
    for param in params_str.split(','):
        key, value = param.split('=')
        # 尝试转换为数值类型
        try:
            value = int(value)
        except ValueError:
            try:
                value = float(value)
            except ValueError:
                pass
        params[key] = value
    
    return params


def parse_param_range(param_range_str):
    """
    解析参数优化范围
    
    Args:
        param_range_str: 参数范围字符串，格式"n=5:15:1,k=1:5:1"
    
    Returns:
        参数范围字典
    """
    if not param_range_str:
        return {}
    
    param_range = {}
    for param in param_range_str.split(','):
        key, value = param.split('=')
        start, end, step = value.split(':')
        param_range[key] = list(range(int(start), int(end) + 1, int(step)))
    
    return param_range


def get_factor_columns_with_sensitivity():
    """
    获取包含敏感度分析字段的因子列列表

    Returns:
        因子列名列表
    """
    # 基础因子列
    factor_columns = ['ts_code', 'trade_date', 'theoretical_price', 'arbitrage_space',
                     'momentum_5d', 'momentum_10d', 'momentum_20d', 'combined_factor']

    # 添加敏感度分析列（advice_2.md改进）
    sensitivity_columns = ['corr_coef', 'reg_slope', 'model_delta', 'bond_floor', 'option_value',
                          'cb_return', 'stock_return', 'predicted_return', 'expected_return', 'stock_close']
    factor_columns.extend(sensitivity_columns)

    # 添加双低因子列
    double_low_columns = ['price_factor', 'premium_factor', 'premium_ratio', 'double_low_score',
                         'double_low_rank', 'double_low_percentile']
    factor_columns.extend(double_low_columns)

    # 添加高级技术因子列
    technical_columns = ['bb_position', 'bb_width', 'bb_deviation', 'volume_percentile', 'amount_percentile',
                        'volume_change_5d', 'volume_change_10d', 'volume_change_20d', 'volume_trend',
                        'volatility_5d', 'volatility_10d', 'volatility_20d', 'atr_14d', 'relative_volatility',
                        'price_percentile', 'price_position', 'drawdown_from_high', 'gain_from_low',
                        'avg_turnover_20d', 'turnover_cv', 'amihud_illiquidity',
                        'trend_slope_5d', 'trend_slope_10d', 'trend_slope_20d', 'trend_consistency',
                        'rsi_14d', 'williams_r', 'obv_trend']
    factor_columns.extend(technical_columns)
    
    # 添加基础价格因子列
    basic_price_columns = ['conv_value', 'premium_ratio']
    factor_columns.extend(basic_price_columns)
    
    # 添加增强因子列（Enhanced Factors）
    enhanced_columns = [
        # 可转债特有因子
        'time_decay_factor', 'forced_call_pressure', 'put_protection_value',
        'conversion_tendency', 'conversion_arbitrage_efficiency', 'stock_limit_impact',
        
        # 市场结构因子  
        'net_inflow_factor', 'relative_strength_vs_market', 'liquidity_shock',
        
        # 交叉组合因子
        'momentum_vol_cross', 'momentum_consistency', 'value_quality_score',
        'arbitrage_efficiency', 'premium_ratio_squared', 'momentum_rank_score',
        'volatility_regime',
        
        # 技术形态因子
        'ma5_ma20_cross', 'ma_slope_5d', 'support_strength',
        'resistance_pressure', 'gap_factor', 'price_volume_divergence'
    ]
    factor_columns.extend(enhanced_columns)
    
    # 添加其他技术指标列（保持兼容性）
    optional_columns = ['macd']
    factor_columns.extend(optional_columns)

    return factor_columns


def run_data_mode(args):
    """
    运行数据模式
    
    Args:
        args: 命令行参数
    """
    logger.info(f"运行数据模式: {args.action}")
    
    # 初始化数据获取器
    data_fetcher = DataFetcher()
    
    if args.action == 'fetch':
        # 获取指定时间段的数据
        start_date = args.start_date or config_manager.get_default_start_date()
        end_date = args.end_date or config_manager.get_default_end_date()
        
        logger.info(f"获取数据: {start_date} - {end_date}")
        data_fetcher.fetch_all_data(start_date, end_date)
    
    elif args.action == 'update':
        # 更新数据到最新
        logger.info("更新数据到最新")
        data_fetcher.update_data()

    elif args.action == 'update_close':
        # 更新已有数据的收盘价字段
        logger.info("更新已有数据的收盘价字段")
        data_fetcher.update_close_for_existing_data()

    elif args.action == 'fetch_stock':
        # 获取单只正股的前复权数据
        if not args.ts_code:
            logger.error("获取正股数据需要指定 --ts_code 参数")
            return

        start_date = args.start_date or config_manager.get_default_start_date()
        end_date = args.end_date or config_manager.get_default_end_date()

        logger.info(f"获取正股数据: {args.ts_code}, {start_date} - {end_date}")

        # 转换日期格式为YYYYMMDD
        start_date_str = start_date.replace('-', '') if start_date else None
        end_date_str = end_date.replace('-', '') if end_date else None

        result = data_fetcher.fetch_stock_daily(
            ts_code=args.ts_code,
            start_date=start_date_str,
            end_date=end_date_str
        )

        if result is not None and not result.empty:
            logger.info(f"成功获取 {len(result)} 条正股数据记录")
            print(f"数据预览:")
            print(result[['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pre_close']].head())
        else:
            logger.warning("未获取到正股数据")

    elif args.action == 'fetch_call':
        # 获取强制赎回数据
        logger.info("获取强制赎回数据")
        data_fetcher.fetch_cb_call()
    
    elif args.action == 'full_price':
        # 更新full_price字段
        start_date = args.start_date or config_manager.get_default_start_date()
        end_date = args.end_date or config_manager.get_default_end_date()
        
        logger.info(f"更新full_price字段: {start_date} - {end_date}")
        data_fetcher.update_close_for_existing_data(force_update=args.force)


def calculate_single_bond_factors(factor_calculator, date, bond_code):
    """
    计算单个可转债的因子

    Args:
        factor_calculator: 因子计算器实例
        date: 日期字符串
        bond_code: 可转债代码

    Returns:
        包含单个债券因子的DataFrame
    """
    print(f"🚀 开始计算 {bond_code} 在 {date} 的因子...")

    try:
        # 使用新的单债券计算方法
        bond_factors = factor_calculator.calculate_factors_for_single_bond(date, bond_code)

        if bond_factors.empty:
            print(f"❌ 债券 {bond_code} 在日期 {date} 没有因子数据")
            return pd.DataFrame()

        print(f"✅ 成功计算 {bond_code} 的因子数据")
        print(f"📈 套利空间: {bond_factors['arbitrage_space'].iloc[0]:.3f}")
        print(f"📈 理论价格: {bond_factors['theoretical_price'].iloc[0]:.3f}")

        return bond_factors

    except Exception as e:
        print(f"❌ 计算单债券因子失败: {e}")
        return pd.DataFrame()


def calculate_single_bond_factors_for_period(factor_calculator, start_date, end_date, bond_code):
    """
    计算单个可转债在指定时间段的因子

    Args:
        factor_calculator: 因子计算器实例
        start_date: 开始日期
        end_date: 结束日期
        bond_code: 可转债代码

    Returns:
        包含单个债券时间段因子的DataFrame
    """
    print(f"🚀 开始计算 {bond_code} 在 {start_date} 到 {end_date} 的因子...")

    try:
        # 对于新的因子计算器，我们需要逐日计算
        from datetime import datetime, timedelta
        import pandas as pd

        start_dt = datetime.strptime(start_date.replace('-', ''), '%Y%m%d')
        end_dt = datetime.strptime(end_date.replace('-', ''), '%Y%m%d')

        all_factors = []
        current_dt = start_dt

        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y%m%d')
            try:
                daily_factors = factor_calculator.calculate_factors_for_single_bond(date_str, bond_code)
                if not daily_factors.empty:
                    all_factors.append(daily_factors)
            except Exception as e:
                print(f"跳过日期 {date_str}: {e}")

            current_dt += timedelta(days=1)

        if not all_factors:
            print(f"❌ 债券 {bond_code} 在时间段 {start_date} 到 {end_date} 没有因子数据")
            return pd.DataFrame()

        bond_factors = pd.concat(all_factors, ignore_index=True)

        print(f"✅ 成功计算 {bond_code} 的时间段因子数据")
        print(f"📅 数据天数: {len(bond_factors)} 天")
        if 'arbitrage_space' in bond_factors.columns:
            print(f"📈 平均套利空间: {bond_factors['arbitrage_space'].mean():.3f}")

        return bond_factors

    except Exception as e:
        print(f"❌ 计算单债券时间段因子失败: {e}")
        return pd.DataFrame()


def run_factor_mode(args):
    """
    运行因子模式

    Args:
        args: 命令行参数
    """
    print("🧮 运行新版因子计算模式")

    # 检查是否启用多线程（--multithreaded 或 --threads/--max-workers 参数都会启用）
    use_multithreaded = getattr(args, 'multithreaded', False) or getattr(args, 'max_workers', None) is not None
    if use_multithreaded:
        print("🚀 启用多线程加速计算")
        return run_multithreaded_factor_mode(args)

    # 获取精度等级
    precision_level = getattr(args, 'precision', 'medium')
    print(f"使用精度等级: {precision_level}")

    # 检查是否指定了单个债券代码
    if getattr(args, 'code', None):
        print(f"🎯 单债券模式: 只更新 {args.code} 的因子")

    # 获取因子计算器类型参数
    calculator_type = getattr(args, 'calculator', 'default')
    alpha = getattr(args, 'alpha', 0.5)

    # 创建因子计算器
    if calculator_type == 'double_low':
        print(f"使用双低组合因子计算器 (α={alpha:.2f})")
        factor_calculator = create_double_low_calculator(
            precision_level=precision_level,
            use_dynamic_params=True,
            alpha=alpha
        )
    elif calculator_type == 'advanced':
        print(f"使用高级组合因子计算器 (包含技术因子, α={alpha:.2f})")
        factor_calculator = create_advanced_calculator(
            precision_level=precision_level,
            use_dynamic_params=True,
            alpha=alpha
        )
    elif calculator_type == 'enhanced':
        print(f"使用增强组合因子计算器 (包含所有因子+新增增强因子, α={alpha:.2f})")
        factor_calculator = create_enhanced_calculator(
            precision_level=precision_level,
            use_dynamic_params=True,
            alpha=alpha
        )
    elif calculator_type == 'zl_only':
        print("使用ZL模型因子计算器")
        factor_calculator = create_calculator_from_args(
            'zl',
            precision_level=precision_level,
            use_dynamic_params=True
        )
    elif calculator_type == 'momentum_only':
        print("使用动量因子计算器")
        factor_calculator = create_calculator_from_args('momentum')
    elif calculator_type == 'technical_only':
        print("使用高级技术因子计算器")
        factor_calculator = create_calculator_from_args('advanced_technical')
    else:
        try:
            factor_calculator = create_calculator_from_args(calculator_type)
            print(f"使用自定义因子计算器: {calculator_type}")
        except ValueError:
            print("使用默认组合因子计算器 (ZL模型 + 动量因子)")
            factor_calculator = create_default_calculator(
                precision_level=precision_level,
                use_dynamic_params=True
            )

    if args.date:
        # 计算指定日期的因子
        if args.code:
            # 单债券模式
            factors = calculate_single_bond_factors(factor_calculator, args.date, args.code)
        else:
            # 全市场模式
            factors = factor_calculator.calculate_factors_for_date(args.date)

        if len(factors) > 0:
            # 选择需要保存的因子列（包含敏感度分析字段）
            factor_columns = get_factor_columns_with_sensitivity()
            
            # 选择存在的列
            available_columns = [col for col in factor_columns if col in factors.columns]
            factors_to_save = factors[available_columns].copy()
            
            # 重命名theoretical_price为zl_price以匹配数据库结构
            if 'theoretical_price' in factors_to_save.columns:
                factors_to_save.rename(columns={'theoretical_price': 'zl_price'}, inplace=True)
            
            # 保存因子数据到数据库（使用INSERT OR REPLACE避免重复）
            import sqlite3
            conn = sqlite3.connect(config_manager.get_db_path())
            cursor = conn.cursor()

            # 使用INSERT OR REPLACE来处理重复数据
            saved_count = 0
            for _, row in factors_to_save.iterrows():
                try:
                    # 构建动态SQL语句
                    columns = list(row.index)
                    placeholders = ', '.join(['?' for _ in columns])
                    column_names = ', '.join(columns)

                    cursor.execute(f'''
                        INSERT OR REPLACE INTO cb_factor ({column_names})
                        VALUES ({placeholders})
                    ''', tuple(row.values))
                    saved_count += 1
                except Exception as e:
                    print(f"保存因子记录失败: {e}")

            conn.commit()
            conn.close()
            print(f"已保存 {saved_count} 条因子记录到数据库")
            print(f"保存的列: {factors_to_save.columns.tolist()}")
        else:
            print("没有计算出因子数据")
    
    elif args.start_date and args.end_date:
        # 计算指定时间段的因子
        if args.code:
            # 单债券时间段模式
            factors = calculate_single_bond_factors_for_period(factor_calculator, args.start_date, args.end_date, args.code)
        else:
            # 全市场时间段模式
            factors = factor_calculator.calculate_factors_for_period(args.start_date, args.end_date)
        if len(factors) > 0:
            # 选择需要保存的因子列（包含敏感度分析字段）
            factor_columns = get_factor_columns_with_sensitivity()
            
            # 选择存在的列
            available_columns = [col for col in factor_columns if col in factors.columns]
            factors_to_save = factors[available_columns].copy()
            
            # 重命名theoretical_price为zl_price以匹配数据库结构
            if 'theoretical_price' in factors_to_save.columns:
                factors_to_save.rename(columns={'theoretical_price': 'zl_price'}, inplace=True)
            
            # 保存因子数据到数据库（使用INSERT OR REPLACE避免重复）
            import sqlite3
            conn = sqlite3.connect(config_manager.get_db_path())
            cursor = conn.cursor()

            # 使用INSERT OR REPLACE来处理重复数据
            saved_count = 0
            for _, row in factors_to_save.iterrows():
                try:
                    # 构建动态SQL语句
                    columns = list(row.index)
                    placeholders = ', '.join(['?' for _ in columns])
                    column_names = ', '.join(columns)

                    cursor.execute(f'''
                        INSERT OR REPLACE INTO cb_factor ({column_names})
                        VALUES ({placeholders})
                    ''', tuple(row.values))
                    saved_count += 1
                except Exception as e:
                    print(f"保存因子记录失败: {e}")

            conn.commit()
            conn.close()
            print(f"已保存 {saved_count} 条因子记录到数据库")
            print(f"保存的列: {factors_to_save.columns.tolist()}")
        else:
            print("没有计算出因子数据")
    
    else:
        # 使用默认日期
        start_date = config_manager.get_default_start_date()
        end_date = config_manager.get_default_end_date()
        
        factors = factor_calculator.calculate_factors_for_period(start_date, end_date)
        if len(factors) > 0:
            # 选择需要保存的因子列（包含敏感度分析字段）
            factor_columns = get_factor_columns_with_sensitivity()
            
            # 选择存在的列
            available_columns = [col for col in factor_columns if col in factors.columns]
            factors_to_save = factors[available_columns].copy()
            
            # 重命名theoretical_price为zl_price以匹配数据库结构
            if 'theoretical_price' in factors_to_save.columns:
                factors_to_save.rename(columns={'theoretical_price': 'zl_price'}, inplace=True)
            
            # 保存因子数据到数据库（使用INSERT OR REPLACE避免重复）
            import sqlite3
            conn = sqlite3.connect(config_manager.get_db_path())
            cursor = conn.cursor()

            # 使用INSERT OR REPLACE来处理重复数据
            saved_count = 0
            for _, row in factors_to_save.iterrows():
                try:
                    # 构建动态SQL语句
                    columns = list(row.index)
                    placeholders = ', '.join(['?' for _ in columns])
                    column_names = ', '.join(columns)

                    cursor.execute(f'''
                        INSERT OR REPLACE INTO cb_factor ({column_names})
                        VALUES ({placeholders})
                    ''', tuple(row.values))
                    saved_count += 1
                except Exception as e:
                    print(f"保存因子记录失败: {e}")

            conn.commit()
            conn.close()
            print(f"已保存 {saved_count} 条因子记录到数据库")
            print(f"保存的列: {factors_to_save.columns.tolist()}")
        else:
            print("没有计算出因子数据")


def run_multithreaded_factor_mode(args):
    """
    运行多线程因子计算模式
    
    Args:
        args: 命令行参数
    """
    print("🚀 运行多线程因子计算模式")
    
    # 导入多线程因子计算器
    from factor.multithreaded_factor_calculator import MultithreadedFactorCalculator
    
    # 获取参数
    calculator_type = getattr(args, 'calculator', 'enhanced')
    precision_level = getattr(args, 'precision', 'medium')
    alpha = getattr(args, 'alpha', 0.5)
    max_workers = getattr(args, 'max_workers', None)
    chunk_size = getattr(args, 'chunk_size', 30)
    factor_filter = getattr(args, 'factor_filter', None)
    
    print(f"计算器类型: {calculator_type}")
    print(f"精度等级: {precision_level}")
    if factor_filter:
        print(f"因子过滤: {factor_filter}")
    
    # 创建多线程因子计算器
    mt_calculator = MultithreadedFactorCalculator(
        calculator_type=calculator_type,
        max_workers=max_workers,
        precision_level=precision_level,
        use_dynamic_params=True,
        alpha=alpha
    )
    
    # 检查是否为单债券模式
    if getattr(args, 'code', None):
        print(f"❌ 多线程模式暂不支持单债券计算，请使用普通模式")
        return
    
    if args.date:
        print(f"❌ 多线程模式暂不支持单日计算，请使用时间段模式")
        return
    
    # 获取时间段
    if args.start_date and args.end_date:
        start_date = args.start_date
        end_date = args.end_date
    else:
        # 使用默认时间段
        start_date = config_manager.get_default_start_date()
        end_date = config_manager.get_default_end_date()
        print(f"使用默认时间段: {start_date} 到 {end_date}")
    
    # 执行多线程计算
    factors = mt_calculator.calculate_factors_for_period(
        start_date=start_date,
        end_date=end_date,
        factor_filter=factor_filter,
        chunk_size=chunk_size
    )
    
    if len(factors) > 0:
        # 选择需要保存的因子列
        factor_columns = get_factor_columns_with_sensitivity()
        
        # 选择存在的列
        available_columns = [col for col in factor_columns if col in factors.columns]
        factors_to_save = factors[available_columns].copy()
        
        # 重命名theoretical_price为zl_price以匹配数据库结构
        if 'theoretical_price' in factors_to_save.columns:
            factors_to_save.rename(columns={'theoretical_price': 'zl_price'}, inplace=True)
        
        # 保存因子数据到数据库
        print("💾 保存因子数据到数据库...")
        print("🔒 使用单线程模式保存数据以避免锁定...")
        saved_count = _save_factors_single_thread(factors_to_save)
        print(f"  总计保存 {saved_count} 条记录")
        print(f"✅ 多线程因子计算完成！")
        print(f"📊 总计保存 {saved_count} 条因子记录到数据库")
        print(f"📈 保存的列: {factors_to_save.columns.tolist()}")
    else:
        print("❌ 没有计算出因子数据")


def _save_factors_single_thread(factors_df):
    """
    使用单线程模式保存因子数据，避免数据库锁定问题
    
    Args:
        factors_df: 要保存的因子数据DataFrame
    
    Returns:
        成功保存的记录数
    """
    import sqlite3
    
    saved_count = 0
    batch_size = 1000  # 可以使用较大的批次大小
    
    conn = None
    try:
        # 建立单一数据库连接
        conn = sqlite3.connect(config_manager.get_db_path(), timeout=60.0)
        
        # 设置数据库优化参数
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=10000")
        conn.execute("PRAGMA temp_store=memory")
        
        cursor = conn.cursor()
        
        # 分批保存数据
        for i in range(0, len(factors_df), batch_size):
            batch = factors_df.iloc[i:i+batch_size]
            
            # 开始事务
            cursor.execute("BEGIN TRANSACTION")
            
            batch_saved = 0
            try:
                for _, row in batch.iterrows():
                    try:
                        # 构建动态SQL语句
                        columns = list(row.index)
                        placeholders = ', '.join(['?' for _ in columns])
                        column_names = ', '.join(columns)
                        
                        cursor.execute(f'''
                            INSERT OR REPLACE INTO cb_factor ({column_names})
                            VALUES ({placeholders})
                        ''', tuple(row.values))
                        batch_saved += 1
                    except Exception as e:
                        print(f"保存单条记录失败: {e}")
                        continue
                
                # 提交事务
                cursor.execute("COMMIT")
                saved_count += batch_saved
                
                print(f"  已保存 {min(i+batch_size, len(factors_df))}/{len(factors_df)} 条记录")
                
            except Exception as e:
                # 回滚事务
                cursor.execute("ROLLBACK")
                print(f"保存批次失败，已回滚: {e}")
                continue
        
    except Exception as e:
        print(f"数据库连接失败: {e}")
    finally:
        if conn:
            conn.close()
    
    return saved_count


def run_incremental_factor_mode(args):
    """
    运行增量因子计算模式
    
    Args:
        args: 命令行参数
    """
    print("🔄 运行增量因子计算模式")
    
    # 获取精度等级
    precision_level = getattr(args, 'precision', 'medium')
    print(f"使用精度等级: {precision_level}")
    
    # 获取因子计算器类型参数
    calculator_type = getattr(args, 'calculator', 'advanced')
    alpha = getattr(args, 'alpha', 0.5)
    incremental = getattr(args, 'incremental', True)
    force_update = getattr(args, 'force_update', False)
    
    # 创建因子计算器（默认使用高级组合）
    if calculator_type == 'advanced':
        print(f"使用高级组合因子计算器 (包含技术因子, α={alpha:.2f})")
        factor_calculator = create_advanced_calculator(
            precision_level=precision_level,
            use_dynamic_params=True,
            alpha=alpha
        )
    elif calculator_type == 'double_low':
        print(f"使用双低组合因子计算器 (α={alpha:.2f})")
        factor_calculator = create_double_low_calculator(
            precision_level=precision_level,
            use_dynamic_params=True,
            alpha=alpha
        )
    else:
        print("使用默认组合因子计算器")
        factor_calculator = create_default_calculator(
            precision_level=precision_level,
            use_dynamic_params=True
        )
    
    # 获取日期参数
    if args.start_date and args.end_date:
        start_date = args.start_date
        end_date = args.end_date
    else:
        # 默认计算最近一个月的增量因子
        from datetime import datetime, timedelta
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        print(f"使用默认时间段: {start_date} 到 {end_date}")
    
    # 执行增量计算
    new_factors_only = incremental and not force_update
    print(f"增量模式: {'只计算新因子' if new_factors_only else '更新所有因子'}")
    
    factors = factor_calculator.calculate_incremental_factors(
        start_date=start_date,
        end_date=end_date,
        new_factors_only=new_factors_only
    )
    
    if len(factors) > 0:
        # 保存增量因子数据
        factor_columns = get_factor_columns_with_sensitivity()
        available_columns = [col for col in factor_columns if col in factors.columns]
        factors_to_save = factors[available_columns].copy()
        
        # 重命名theoretical_price为zl_price以匹配数据库结构
        if 'theoretical_price' in factors_to_save.columns:
            factors_to_save.rename(columns={'theoretical_price': 'zl_price'}, inplace=True)
        
        # 保存到数据库
        saved_count = _save_factors_single_thread(factors_to_save)
        print(f"✅ 已保存 {saved_count} 条增量因子记录到数据库")
        print(f"保存的列: {factors_to_save.columns.tolist()}")
    else:
        print("没有计算出增量因子数据")


def run_ml_model_mode(args):
    """
    运行AutoGluon机器学习模型模式
    
    Args:
        args: 命令行参数
    """
    from factor.autogluon_predictor import (
        AutoGluonFactorPredictor, train_autogluon_predictor,
        load_and_predict_autogluon
    )
    
    print("[AI] AutoGluon机器学习模型模式")
    
    # 列出模型
    if args.list_ml_models:
        print("\n📋 可用AutoGluon模型列表:")
        predictor = AutoGluonFactorPredictor()
        predictor.list_available_models()
        return
    
    # 训练新模型
    if args.train_ml_model:
        print(f"\n[训练] 训练新AutoGluon模型: {args.ml_model_name}")
        
        # 解析日期参数
        def convert_date_format(date_str):
            if date_str:
                return date_str.replace('-', '')
            return None
        
        train_start = convert_date_format(args.start_date) or '20240101'
        train_end = convert_date_format(args.end_date) or '20240630'
        
        # 处理因子配置 - 使用统一的配置处理逻辑
        custom_features = args.custom_factors
        if not custom_features and hasattr(args, 'factor_config'):
            # 导入统一的因子配置处理函数
            from factor.autogluon_predictor import get_factor_config_by_name
            
            # 根据factor_config选择因子
            config_features = get_factor_config_by_name(args.factor_config)
            if config_features is not None:
                custom_features = config_features
                print(f"使用{args.factor_config}因子配置: {len(custom_features)}个因子")
            else:
                print("使用default因子配置")
        
        # 训练模型
        predictor = train_autogluon_predictor(
            train_start=train_start,
            train_end=train_end,
            model_name=args.ml_model_name,
            target_days=args.target_days,
            time_limit=args.time_limit,
            presets=args.presets,
            custom_features=custom_features,
            preprocessing_config=args.preprocessing_config,
            use_sample_weights=args.use_sample_weights,
            sorted_feature=args.sorted_feature
        )
        
        if predictor:
            print(f"✅ AutoGluon模型训练完成: {args.ml_model_name}")
            
            # 显示训练结果
            if predictor.training_results:
                results = predictor.training_results
                print(f"\n📊 训练结果:")
                
                if 'r2' in results:
                    print(f"  R²: {results['r2']:.4f}")
                if 'rmse' in results:
                    print(f"  RMSE: {results['rmse']:.6f}")
                if 'test_r2' in results:
                    print(f"  测试R²: {results['test_r2']:.4f}")
                if 'test_rmse' in results:
                    print(f"  测试RMSE: {results['test_rmse']:.6f}")
        else:
            print("❌ AutoGluon模型训练失败")
        
        return
    
    # 使用模型进行预测
    if args.use_ml_model:
        print(f"\n🔮 使用AutoGluon模型进行预测: {args.ml_model_name}")
        
        # 加载因子数据
        import sqlite3
        conn = sqlite3.connect(config_manager.get_db_path())
        
        # 获取最新的因子数据（使用所有可用因子）
        latest_date_query = "SELECT MAX(trade_date) as max_date FROM cb_factor"
        latest_date_result = pd.read_sql(latest_date_query, conn)
        latest_date = latest_date_result['max_date'].iloc[0]

        print(f"📅 获取最新日期的因子数据: {latest_date}")

        # 使用改进的因子获取方法
        factor_data, available_factors = get_factor_data_with_all_factors(latest_date, latest_date, conn)
        conn.close()

        if factor_data is None or factor_data.empty:
            print("❌ 没有找到因子数据")
            return

        print(f"📊 加载因子数据: {len(factor_data)} 条记录，包含 {len(available_factors)} 个因子")
        
        # 使用模型预测
        result_data = load_and_predict_autogluon(
            model_name=args.ml_model_name,
            factor_data=factor_data,
            factor_name='ml_predicted_return'
        )
        
        if 'ml_predicted_return' in result_data.columns:
            print("✅ 预测完成")
            
            # 显示预测结果统计
            predictions = result_data['ml_predicted_return']
            print(f"预测结果统计:")
            print(f"  均值: {predictions.mean():.4f}")
            print(f"  标准差: {predictions.std():.4f}")
            print(f"  最小值: {predictions.min():.4f}")
            print(f"  最大值: {predictions.max():.4f}")
            
            # 显示排名前10的债券
            top_bonds = result_data.nlargest(10, 'ml_predicted_return')[['ts_code', 'ml_predicted_return']]
            print(f"\n🏆 预测排名前10的债券:")
            for i, (_, row) in enumerate(top_bonds.iterrows(), 1):
                print(f"  {i:2d}. {row['ts_code']}: {row['ml_predicted_return']:.4f}")
        else:
            print("❌ 预测失败")
        
        return
    
    # 默认显示帮助
    print("\n使用说明:")
    print("  --list-ml-models        列出所有可用的AutoGluon模型")
    print("  --train-ml-model        训练新的AutoGluon模型")
    print("  --use-ml-model          使用AutoGluon模型进行预测")
    print("\n示例:")
    print("  python main.py --mode ml_model --list-ml-models")
    print("  python main.py --mode ml_model --train-ml-model --ml-model-name my_model --start-date 2024-01-01 --end-date 2024-06-30")
    print("  python main.py --mode ml_model --use-ml-model --ml-model-name my_model")


def run_ft_transformer_model_mode(args):
    """
    运行FT-Transformer模型模式
    
    Args:
        args: 命令行参数
    """
    from factor.ft_transformer_predictor import (
        FTTransformerPredictor, train_ft_transformer_predictor,
        load_and_predict_ft_transformer
    )
    
    print("[AI] FT-Transformer深度学习模型模式")
    
    # 列出模型
    if args.list_ml_models:
        print("\n📋 可用FT-Transformer模型列表:")
        predictor = FTTransformerPredictor()
        predictor.list_available_models()
        return
    
    # 训练新模型
    if args.train_ml_model:
        print(f"\n[训练] 训练新FT-Transformer模型: {args.ml_model_name}")
        
        # 解析日期参数
        def convert_date_format(date_str):
            if date_str:
                return date_str.replace('-', '')
            return None
        
        train_start = convert_date_format(args.start_date) or '20240101'
        train_end = convert_date_format(args.end_date) or '20240630'
        
        # 处理因子配置 - 使用统一的配置处理逻辑
        custom_features = args.custom_factors
        if not custom_features and hasattr(args, 'factor_config'):
            # 导入统一的因子配置处理函数
            from factor.autogluon_predictor import get_factor_config_by_name
            
            # 根据factor_config选择因子
            config_features = get_factor_config_by_name(args.factor_config)
            if config_features is not None:
                custom_features = config_features
                print(f"使用{args.factor_config}因子配置: {len(custom_features)}个因子")
            else:
                print("使用default因子配置")
        
        # 训练模型
        predictor = train_ft_transformer_predictor(
            train_start=train_start,
            train_end=train_end,
            model_name=args.ml_model_name,
            target_days=args.target_days,
            time_limit=args.time_limit,
            custom_features=custom_features,
            preprocessing_config=args.preprocessing_config,
            tuning_ratio=0.1,
            use_sample_weights=getattr(args, 'use_sample_weights', False),
            sorted_feature=getattr(args, 'sorted_feature', False)
        )
        
        if predictor:
            print(f"✅ FT-Transformer模型训练完成: {args.ml_model_name}")
            
            # 显示训练结果
            if predictor.training_results:
                results = predictor.training_results
                print(f"\n📊 训练结果:")
                
                if 'train_r2' in results:
                    print(f"  训练R²: {results['train_r2']:.4f}")
                if 'train_rmse' in results:
                    print(f"  训练RMSE: {results['train_rmse']:.6f}")
                if 'tuning_r2' in results:
                    print(f"  验证R²: {results['tuning_r2']:.4f}")
                if 'tuning_rmse' in results:
                    print(f"  验证RMSE: {results['tuning_rmse']:.6f}")
                if 'train_samples' in results:
                    print(f"  训练样本: {results['train_samples']}")
        else:
            print("❌ FT-Transformer模型训练失败")
        
        return
    
    # 使用模型进行预测
    if args.use_ml_model:
        print(f"\n🔮 使用FT-Transformer模型进行预测: {args.ml_model_name}")
        
        # 解析日期参数
        def convert_date_format(date_str):
            if date_str:
                return date_str.replace('-', '')
            return None
        
        # 使用用户指定的日期范围，如果没有指定则使用最新日期
        if args.start_date and args.end_date:
            start_date = convert_date_format(args.start_date) 
            end_date = convert_date_format(args.end_date)
            print(f"📅 使用指定日期范围的因子数据: {start_date} - {end_date}")
        else:
            # 加载因子数据
            import sqlite3
            conn = sqlite3.connect(config_manager.get_db_path())
            
            # 获取最新的因子数据（使用所有可用因子）
            latest_date_query = "SELECT MAX(trade_date) as max_date FROM cb_factor"
            latest_date_result = pd.read_sql(latest_date_query, conn)
            latest_date = latest_date_result['max_date'].iloc[0]
            start_date = end_date = latest_date
            conn.close()
            print(f"📅 获取最新日期的因子数据: {latest_date}")

        # 加载因子数据
        import sqlite3
        conn = sqlite3.connect(config_manager.get_db_path())
        
        # 使用改进的因子获取方法
        factor_data, available_factors = get_factor_data_with_all_factors(start_date, end_date, conn)
        conn.close()

        if factor_data is None or factor_data.empty:
            print("❌ 没有找到因子数据")
            return

        print(f"📊 加载因子数据: {len(factor_data)} 条记录，包含 {len(available_factors)} 个因子")
        
        # 使用模型预测
        result_data = load_and_predict_ft_transformer(
            model_name=args.ml_model_name,
            factor_data=factor_data,
            factor_name='ft_predicted_return'
        )
        
        if 'ft_predicted_return' in result_data.columns:
            print("✅ 预测完成")
            
            # 显示预测结果统计
            predictions = result_data['ft_predicted_return']
            print(f"预测结果统计:")
            print(f"  均值: {predictions.mean():.4f}")
            print(f"  标准差: {predictions.std():.4f}")
            print(f"  最小值: {predictions.min():.4f}")
            print(f"  最大值: {predictions.max():.4f}")
            
            # 显示排名前10的债券
            top_bonds = result_data.nlargest(10, 'ft_predicted_return')[['ts_code', 'ft_predicted_return']]
            print(f"\n🏆 预测排名前10的债券:")
            for i, (_, row) in enumerate(top_bonds.iterrows(), 1):
                print(f"  {i:2d}. {row['ts_code']}: {row['ft_predicted_return']:.4f}")
        else:
            print("❌ 预测失败")
        
        return
    
    # 默认显示帮助
    print("\n使用说明:")
    print("  --list-ft-models        列出所有可用的FT-Transformer模型")
    print("  --train-ft-model        训练新的FT-Transformer模型")
    print("  --use-ft-model          使用FT-Transformer模型进行预测")
    print("\n示例:")
    print("  python main.py --mode ft_transformer --list-ft-models")
    print("  python main.py --mode ft_transformer --train-ft-model --ft-model-name my_ft_model --start-date 2024-01-01 --end-date 2024-06-30")
    print("  python main.py --mode ft_transformer --use-ft-model --ft-model-name my_ft_model")


def run_backtest_mode(args):
    """
    运行优化版回测模式（集成最佳实践）
    
    Args:
        args: 命令行参数
    """
    # 解析日期参数
    start_date = args.start_date or config_manager.get_default_start_date()
    end_date = args.end_date or config_manager.get_default_end_date()
    
    # 解析策略参数（使用最优配置）
    strategy_name = args.strategy or config_manager.get_default_strategy()
    params = parse_params(args.params)
    
    # 如果没有指定参数，使用最优参数
    if not params:
        params = {'n': 10, 'k': 2}  # 最优参数
    
    # 获取其他参数
    precision_level = getattr(args, 'precision', 'medium')
    rebalance_frequency = getattr(args, 'rebalance_frequency', 'weekly')
    transaction_cost = getattr(args, 'transaction_cost', 0.002)
    factor_ascending = getattr(args, 'factor_ascending', False)
    factor_name = getattr(args, 'factor_name', 'combined_factor')
    filter_type = getattr(args, 'filter', 'none')

    # 创建筛选器
    try:
        bond_filter = create_filter_from_args(filter_type)
        print(f"筛选器: {bond_filter.get_name()}")
    except ValueError as e:
        print(f"筛选器创建失败: {e}")
        return

    print(f"🚀 运行优化版回测模式")
    print(f"回测期间: {start_date} - {end_date}")
    print(f"策略: {strategy_name}, 参数: {params}")
    print(f"调整频率: {rebalance_frequency}")
    print(f"交易成本费率: {transaction_cost:.3%}")
    print(f"使用因子: {factor_name}")
    print(f"因子排序: {'升序' if factor_ascending else '降序'}（因子值{'低' if factor_ascending else '高'}的优先）")
    print(f"计算精度: {precision_level}")
    print(f"预筛选策略: {bond_filter.get_name()}")
    
    # 创建策略（使用最优参数）
    if strategy_name == 'topndropoutk':
        n = params.get('n', 10)  # 默认使用最优参数
        k = params.get('k', 2)
        d = params.get('d', 40)  # 保护排名阈值，默认值40
        strategy = TopNDropoutKStrategy(n=n, k=k, d=d)
    else:
        print(f"未知策略: {strategy_name}")
        return
    
    # 检查是否使用线性回归模型
    linear_model_name = None
    if hasattr(args, 'use_linear_model') and args.use_linear_model:
        linear_model_name = getattr(args, 'model_name', 'default')
        print(f"使用线性回归模型: {linear_model_name}")
    
    # 检查是否使用ML模型
    ml_model_name = None
    if hasattr(args, 'use_ml_model') and args.use_ml_model:
        ml_model_name = getattr(args, 'ml_model_name', 'default_ml')
        print(f"使用AutoGluon ML模型: {ml_model_name}")

    # 检查是否使用FT-Transformer模型
    ft_model_name = None
    if hasattr(args, 'use_ft_model') and args.use_ft_model:
        ft_model_name = getattr(args, 'ft_model_name', 'default_ft')
        print(f"使用FT-Transformer模型: {ft_model_name}")

    # 获取初始资金
    initial_capital = config_manager.get_default_initial_capital()
    
    # 初始化优化版回测器
    backtester = Backtester(
        start_date=start_date,
        end_date=end_date,
        strategy=strategy,
        initial_capital=initial_capital,
        precision_level=precision_level,
        rebalance_frequency=rebalance_frequency
    )

    # 设置筛选器
    backtester.set_filter(bond_filter)

    # 设置线性回归模型（如果指定）
    if linear_model_name:
        factor_config_name = getattr(args, 'factor_config', 'default')
        backtester.set_linear_model(linear_model_name, factor_config_name)
    
    # 设置ML模型（如果指定）
    if ml_model_name:
        factor_config_name = getattr(args, 'factor_config', 'default')
        print(f"使用AutoGluon ML模型: {ml_model_name}，因子配置: {factor_config_name}")
        backtester.set_ml_model(ml_model_name)
    
    # 设置FT-Transformer模型（如果指定）
    if ft_model_name:
        factor_config_name = getattr(args, 'factor_config', 'default')
        print(f"使用FT-Transformer模型: {ft_model_name}，因子配置: {factor_config_name}")
        backtester.set_ft_model(ft_model_name)

    # 运行回测
    results = backtester.run(factor_name=factor_name, ascending=factor_ascending)
    
    # 输出回测结果
    if results and 'performance' in results:
        performance = results['performance']
        
        # 输出简化的交易统计
        if 'results' in results and 'trades' in results['results']:
            _print_simplified_trading_summary(results['results']['trades'], end_date)
        
        print(f"\n🎯 回测结果:")
        print(f"总收益率: {performance.get('total_return', 0):.2%}")
        print(f"年化收益率: {performance.get('annual_return', 0):.2%}")
        print(f"最大回撤: {performance.get('max_drawdown', 0):.2%}")
        print(f"夏普比率: {performance.get('sharpe_ratio', 0):.2f}")
        print(f"年化波动率: {performance.get('volatility', 0):.2%}")
        print(f"胜率: {performance.get('win_rate', 0):.2%}")
        print(f"总交易次数: {performance.get('total_trades', 0)}")

        # 输出交易成本信息
        if 'total_transaction_cost' in performance:
            print(f"总交易成本: {performance.get('total_transaction_cost', 0):,.0f}")
            print(f"成本占初始资金比例: {performance.get('cost_ratio', 0):.2%}")

        # 输出报告路径
        if 'report' in results:
            print(f"\n📊 回测报告: {results['report']}")
    else:
        print("回测失败或无结果")


def run_realtime_mode(args):
    """
    运行实时模拟交易模式
    
    Args:
        args: 命令行参数
    """
    logger.info("运行实时模拟交易模式")
    
    # 解析策略参数
    strategy_name = args.strategy or config_manager.get_default_strategy()
    params = parse_params(args.params)
    
    # 创建策略
    if strategy_name == 'topndropoutk':
        n = params.get('n', config_manager.get_default_n())
        k = params.get('k', config_manager.get_default_k())
        d = params.get('d', 40)  # 保护排名阈值，默认值40
        strategy = TopNDropoutKStrategy(n=n, k=k, d=d)
    else:
        logger.error(f"未知策略: {strategy_name}")
        return
    
    # 初始化实时模拟交易器
    simulator = RealtimeSimulator(
        strategy=strategy,
        initial_capital=config_manager.get_default_initial_capital()
    )
    
    # 运行一次实时模拟交易流程
    logger.info(f"运行实时模拟交易: 策略: {strategy_name}, 参数: {params}")
    simulator.run_once()


def run_optimize_mode(args):
    """
    运行参数优化模式
    
    Args:
        args: 命令行参数
    """
    logger.info("运行参数优化模式")
    
    # 解析日期参数
    start_date = args.start_date or config_manager.get_default_start_date()
    end_date = args.end_date or config_manager.get_default_end_date()
    
    # 解析策略参数
    strategy_name = args.strategy or config_manager.get_default_strategy()
    param_range = parse_param_range(args.param_range)
    
    if not param_range:
        logger.error("未指定参数优化范围")
        return
    
    # 定义回测函数
    def backtest_func(params):
        # 创建策略
        if strategy_name == 'topndropoutk':
            n = params.get('n', config_manager.get_default_n())
            k = params.get('k', config_manager.get_default_k())
            d = params.get('d', 40)  # 保护排名阈值，默认值40
            strategy = TopNDropoutKStrategy(n=n, k=k, d=d)
        else:
            logger.error(f"未知策略: {strategy_name}")
            return None
        
        # 初始化回测器
        backtester = Backtester(
            start_date=start_date,
            end_date=end_date,
            strategy=strategy,
            initial_capital=config_manager.get_default_initial_capital()
        )
        
        # 运行回测
        results = backtester.run()
        
        return results
    
    # 初始化参数优化器
    optimizer = ParameterOptimizer(backtest_func)
    
    # 运行网格搜索
    logger.info(f"运行参数优化: {start_date} - {end_date}, 策略: {strategy_name}, 参数范围: {param_range}")
    best_params, best_result = optimizer.grid_search(param_range)
    
    # 输出优化结果
    logger.info(f"最优参数: {best_params}")
    if best_result and 'performance' in best_result:
        performance = best_result['performance']
        logger.info(f"最优结果:")
        logger.info(f"总收益率: {performance.get('total_return', 0):.2%}")
        logger.info(f"年化收益率: {performance.get('annual_return', 0):.2%}")
        logger.info(f"最大回撤: {performance.get('max_drawdown', 0):.2%}")
        logger.info(f"夏普比率: {performance.get('sharpe_ratio', 0):.2f}")


def run_linear_model_mode(args):
    """
    运行线性回归模型模式

    Args:
        args: 命令行参数
    """
    from factor.linear_regression_optimizer import (
        FactorLinearRegression, train_factor_regression,
        load_and_predict, list_models, create_factor_config
    )

    print("[AI] 线性回归模型模式")

    # 列出模型
    if args.list_models:
        print("\n📋 可用模型列表:")
        list_models()
        return

    # 训练新模型
    if args.train_model:
        print(f"\n[训练] 训练新模型: {args.model_name}")

        # 解析日期参数
        def convert_date_format(date_str):
            if date_str:
                return date_str.replace('-', '')
            return None

        train_start = convert_date_format(args.start_date) or '20240101'
        train_end = convert_date_format(args.end_date) or '20240630'

        # 处理因子配置 - 与ML模型保持统一的处理逻辑
        factor_config = None
        custom_features = args.custom_factors
        
        if not custom_features and hasattr(args, 'factor_config'):
            # 导入统一的因子配置处理函数
            from factor.autogluon_predictor import get_factor_config_by_name
            
            # 根据factor_config选择因子
            config_features = get_factor_config_by_name(args.factor_config)
            if config_features is not None:
                custom_features = config_features
                print(f"使用{args.factor_config}因子配置: {len(custom_features)}个因子")
        
        # 创建因子配置
        if custom_features:
            factor_config = create_factor_config(
                zl_factors=[f for f in custom_features if 'arbitrage' in f or 'zl' in f],
                momentum_factors=[f for f in custom_features if 'momentum' in f],
                double_low_factors=[f for f in custom_features if 'double_low' in f or 'price_factor' in f or 'premium_factor' in f]
            )

        # 训练模型
        # 优先使用target_days参数，如果没有则使用return_days
        target_days = getattr(args, 'target_days', None) or args.return_days
        
        model = train_factor_regression(
            train_start=train_start,
            train_end=train_end,
            regularization=args.regularization,
            alpha=args.reg_alpha,
            model_name=args.model_name,
            factor_config=factor_config,
            target_factor=args.target_factor,
            return_days=target_days,
            custom_factors=custom_features,
            save_model=True,
            factor_config_name=args.factor_config,
            use_sample_weights=getattr(args, 'use_sample_weights', False),
            sorted_feature=getattr(args, 'sorted_feature', False)
        )

        if model:
            print(f"✅ 模型训练完成: {args.model_name}")

            # 显示权重信息
            weights = model.get_factor_weights()
            if weights:
                print("\n📊 因子权重:")
                for factor, info in weights.items():
                    print(f"  {factor}: {info['weight']:.4f} (系数: {info['coefficient']:.4f})")
        else:
            print("❌ 模型训练失败")

        return

    # 使用模型进行预测
    if args.use_linear_model:
        print(f"\n🔮 使用模型进行预测: {args.model_name}")

        # 加载因子数据
        import sqlite3
        from config.config import config_manager

        conn = sqlite3.connect(config_manager.get_db_path())

        # 获取最新的因子数据（使用所有可用因子）
        latest_date_query = "SELECT MAX(trade_date) as max_date FROM cb_factor"
        latest_date_result = pd.read_sql(latest_date_query, conn)
        latest_date = latest_date_result['max_date'].iloc[0]

        print(f"📅 获取最新日期的因子数据: {latest_date}")

        # 使用改进的因子获取方法
        factor_data, available_factors = get_factor_data_with_all_factors(latest_date, latest_date, conn)
        conn.close()

        if factor_data is None or factor_data.empty:
            print("❌ 没有找到因子数据")
            return

        print(f"📊 加载因子数据: {len(factor_data)} 条记录，包含 {len(available_factors)} 个因子")

        # 使用模型预测
        result_data = load_and_predict(
            model_name=args.model_name,
            factor_data=factor_data,
            factor_name='linear_combined_factor'
        )

        if 'linear_combined_factor' in result_data.columns:
            print("✅ 预测完成")

            # 显示预测结果统计
            predictions = result_data['linear_combined_factor']
            print(f"预测结果统计:")
            print(f"  均值: {predictions.mean():.4f}")
            print(f"  标准差: {predictions.std():.4f}")
            print(f"  最小值: {predictions.min():.4f}")
            print(f"  最大值: {predictions.max():.4f}")

            # 显示排名前10的债券
            top_bonds = result_data.nlargest(10, 'linear_combined_factor')[['ts_code', 'linear_combined_factor']]
            print(f"\n🏆 预测排名前10的债券:")
            for i, (_, row) in enumerate(top_bonds.iterrows(), 1):
                print(f"  {i:2d}. {row['ts_code']}: {row['linear_combined_factor']:.4f}")
        else:
            print("❌ 预测失败")

        return

    # 默认显示帮助
    print("\n使用说明:")
    print("  --list-models          列出所有可用模型")
    print("  --train-model          训练新模型")
    print("  --use-linear-model     使用模型进行预测")
    print("\n示例:")
    print("  python main.py --mode linear_model --list-models")
    print("  python main.py --mode linear_model --train-model --model-name my_model --start-date 2024-01-01 --end-date 2024-06-30")
    print("  python main.py --mode linear_model --use-linear-model --model-name my_model")



def run_data_check_mode(args):
    """
    运行数据检查和清理模式

    Args:
        args: 命令行参数
    """
    print("🧹 运行数据检查和清理模式")
    print("="*60)

    # 首先运行数据异常检查
    print("第一步: 数据异常检查")
    print("-"*40)

    try:
        # 使用内置的数据状态检查功能
        print("运行数据状态检查...")
        # 创建临时参数对象用于状态检查
        class TempArgs:
            pass
        temp_args = TempArgs()
        
        # 运行数据状态检查（简化版）
        conn = sqlite3.connect(config_manager.get_db_path())
        
        # 检查数据库表是否存在
        tables_query = "SELECT name FROM sqlite_master WHERE type='table'"
        tables = pd.read_sql(tables_query, conn)['name'].tolist()
        
        required_tables = ['cb_basic', 'cb_daily', 'stock_daily', 'cb_factor']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"⚠️  缺失数据表: {', '.join(missing_tables)}")
        else:
            print("✅ 数据异常检查完成 - 所有必要数据表都存在")
        
        conn.close()
        
    except Exception as e:
        print(f"⚠️  数据异常检查失败: {e}")

    print("\n第二步: 数据清理")
    print("-"*40)

    # 初始化数据清理器
    data_cleaner = DataCleaner()

    # 执行数据清理
    data_cleaner.clean_all_data()

    print("\n第三步: 清理后验证")
    print("-"*40)

    # 运行清理后数据质量验证
    try:
        print("运行清理后数据质量验证...")
        
        conn = sqlite3.connect(config_manager.get_db_path())
        
        # 检查数据质量指标
        quality_checks = {
            '空值记录': "SELECT COUNT(*) FROM cb_daily WHERE close IS NULL OR close <= 0",
            '异常价格': "SELECT COUNT(*) FROM cb_daily WHERE close > 1000",
            '价格突变': "SELECT COUNT(*) FROM cb_daily WHERE ABS(pct_chg) > 50"
        }
        
        total_issues = 0
        for check_name, query in quality_checks.items():
            try:
                result = pd.read_sql(query, conn)
                issue_count = result.iloc[0, 0]
                total_issues += issue_count
                status = "✅" if issue_count == 0 else f"⚠️  {issue_count}条"
                print(f"  {check_name}: {status}")
            except Exception as e:
                print(f"  {check_name}: ❌ 检查失败")
        
        if total_issues == 0:
            print("✅ 清理后数据质量验证完成 - 数据质量良好")
        else:
            print(f"⚠️  发现 {total_issues} 个数据质量问题")
        
        conn.close()
        
    except Exception as e:
        print(f"⚠️  清理后验证失败: {e}")

    print(f"\n🎉 数据检查和清理流程完成！")
    print(f"💡 建议接下来:")
    print(f"  1. 重新计算因子: python main.py --mode factor --precision medium")
    print(f"  2. 运行回测验证: python main.py --mode backtest")


def run_status_mode(args):
    """
    运行数据状态检查模式

    Args:
        args: 命令行参数
    """
    import sqlite3
    import pandas as pd

    print("🔍 数据状态检查报告")
    print("=" * 80)

    conn = sqlite3.connect(config_manager.get_db_path())

    try:
        # 检查数据库表是否存在
        tables_query = "SELECT name FROM sqlite_master WHERE type='table'"
        tables = pd.read_sql(tables_query, conn)['name'].tolist()

        print(f"\n📊 数据库表状态:")
        required_tables = ['cb_basic', 'cb_daily', 'stock_daily', 'cb_factor', 'cb_price_chg', 'cb_call']
        for table in required_tables:
            status = "✅ 存在" if table in tables else "❌ 缺失"
            print(f"  {table:<15}: {status}")

        if 'cb_daily' in tables:
            print(f"\n📈 可转债日行情数据状态:")
            daily_query = '''
            SELECT
                substr(trade_date, 1, 4) as year,
                COUNT(*) as total_records,
                COUNT(DISTINCT ts_code) as unique_bonds,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM cb_daily
            GROUP BY substr(trade_date, 1, 4)
            ORDER BY year
            '''
            daily_data = pd.read_sql(daily_query, conn)
            print(daily_data.to_string(index=False))

        if 'stock_daily' in tables:
            print(f"\n📊 正股日行情数据状态:")
            stock_query = '''
            SELECT
                substr(trade_date, 1, 4) as year,
                COUNT(*) as total_records,
                COUNT(DISTINCT ts_code) as unique_bonds,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM stock_daily
            GROUP BY substr(trade_date, 1, 4)
            ORDER BY year
            '''
            stock_data = pd.read_sql(stock_query, conn)
            print(stock_data.to_string(index=False))

        if 'cb_factor' in tables:
            print(f"\n🧮 因子数据状态:")
            factor_query = '''
            SELECT
                substr(trade_date, 1, 4) as year,
                COUNT(*) as total_records,
                COUNT(DISTINCT ts_code) as unique_bonds,
                COUNT(CASE WHEN zl_price IS NOT NULL THEN 1 END) as zl_price_count,
                COUNT(CASE WHEN arbitrage_space IS NOT NULL THEN 1 END) as arbitrage_space_count,
                COUNT(CASE WHEN momentum_5d IS NOT NULL THEN 1 END) as momentum_5d_count,
                COUNT(CASE WHEN momentum_10d IS NOT NULL THEN 1 END) as momentum_10d_count,
                COUNT(CASE WHEN momentum_20d IS NOT NULL THEN 1 END) as momentum_20d_count,
                COUNT(CASE WHEN combined_factor IS NOT NULL THEN 1 END) as combined_factor_count,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM cb_factor
            GROUP BY substr(trade_date, 1, 4)
            ORDER BY year
            '''
            factor_data = pd.read_sql(factor_query, conn)
            if len(factor_data) > 0:
                print(factor_data.to_string(index=False))
            else:
                print("  ❌ 无因子数据")

        # 检查数据完整性
        print(f"\n🔍 数据完整性分析:")

        # 检查2020-2025年每年的数据状态
        years_to_check = ['2020', '2021', '2022', '2023', '2024', '2025']

        print(f"\n{'年份':<6} {'日行情':<10} {'因子数据':<10} {'状态':<15}")
        print("-" * 50)

        for year in years_to_check:
            # 检查日行情数据
            daily_count_query = f'''
            SELECT COUNT(*) as count FROM cb_daily
            WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
            '''
            daily_count = pd.read_sql(daily_count_query, conn).iloc[0]['count']

            # 检查因子数据
            factor_count_query = f'''
            SELECT COUNT(*) as count FROM cb_factor
            WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
            AND arbitrage_space IS NOT NULL
            '''
            factor_count = pd.read_sql(factor_count_query, conn).iloc[0]['count']

            # 判断状态
            daily_status = "✅" if daily_count > 0 else "❌"
            factor_status = "✅" if factor_count > 0 else "❌"

            if daily_count > 0 and factor_count > 0:
                overall_status = "🟢 完整"
            elif daily_count > 0 and factor_count == 0:
                overall_status = "🟡 缺因子"
            elif daily_count == 0 and factor_count == 0:
                overall_status = "🔴 无数据"
            else:
                overall_status = "🟠 异常"

            print(f"{year:<6} {daily_status:<10} {factor_status:<10} {overall_status:<15}")

        # 给出建议
        print(f"\n💡 建议:")

        # 检查哪些年份缺少因子数据
        missing_factor_years = []
        for year in years_to_check:
            factor_count_query = f'''
            SELECT COUNT(*) as count FROM cb_factor
            WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
            AND arbitrage_space IS NOT NULL
            '''
            factor_count = pd.read_sql(factor_count_query, conn).iloc[0]['count']
            if factor_count == 0:
                # 检查是否有日行情数据
                daily_count_query = f'''
                SELECT COUNT(*) as count FROM cb_daily
                WHERE trade_date >= '{year}0101' AND trade_date <= '{year}1231'
                '''
                daily_count = pd.read_sql(daily_count_query, conn).iloc[0]['count']
                if daily_count > 0:
                    missing_factor_years.append(year)

        if missing_factor_years:
            print(f"  📝 需要计算因子数据的年份: {', '.join(missing_factor_years)}")
            print(f"  🚀 建议运行: python main.py --mode factor --start_date {missing_factor_years[0]}0101 --end_date {missing_factor_years[-1]}1231 --precision medium")
        else:
            print(f"  ✅ 所有年份的因子数据都已完整")

        # 检查最新数据日期
        latest_daily_query = "SELECT MAX(trade_date) as latest_date FROM cb_daily"
        latest_daily = pd.read_sql(latest_daily_query, conn).iloc[0]['latest_date']

        latest_factor_query = "SELECT MAX(trade_date) as latest_date FROM cb_factor"
        latest_factor_result = pd.read_sql(latest_factor_query, conn).iloc[0]['latest_date']
        latest_factor = latest_factor_result if latest_factor_result else "无数据"

        print(f"\n📅 最新数据日期:")
        print(f"  日行情数据: {latest_daily}")
        print(f"  因子数据: {latest_factor}")

        if latest_factor != "无数据" and latest_daily != latest_factor:
            print(f"  ⚠️  因子数据滞后，建议更新因子数据到 {latest_daily}")

    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")

    finally:
        conn.close()

    print("\n" + "=" * 80)








def _print_simplified_trading_summary(trades, end_date):
    """
    输出简化的交易统计信息，包括每日买卖记录和强赎处理

    Args:
        trades: 交易记录列表
        end_date: 回测结束日期
    """
    from collections import defaultdict

    print(f"\n📊 交易统计:")
    print(f"总交易次数: {len(trades)}")

    # 按日期分组交易记录
    daily_trades = defaultdict(list)
    for trade in trades:
        daily_trades[trade['date']].append(trade)

    # 输出每日交易记录
    for date in sorted(daily_trades.keys()):
        day_trades = daily_trades[date]

        # 分类交易
        buys = [t for t in day_trades if t['action'] == 'buy']
        sells = [t for t in day_trades if t['action'] in ['sell', 'forced_sell']]
        forced_redeems = [t for t in day_trades if t['action'] == 'forced_redeem']

        if buys or sells or forced_redeems:
            print(f"\n{date}:")

            # 买入记录
            for trade in buys:
                print(f"  买入 {trade['ts_code']} {trade['quantity']}张 @{trade['price']:.3f}元")

            # 卖出记录
            for trade in sells:
                cost_basis = _get_cost_basis(trade['ts_code'], trades, trade['date'])
                if cost_basis > 0:
                    return_pct = (trade['price'] - cost_basis) / cost_basis * 100
                    profit_loss = "盈利" if return_pct > 0 else "亏损"
                    print(f"  卖出 {trade['ts_code']} {trade['quantity']}张 @{trade['price']:.3f}元 ({profit_loss}: {abs(return_pct):.1f}%)")
                else:
                    print(f"  卖出 {trade['ts_code']} {trade['quantity']}张 @{trade['price']:.3f}元")

            # 强赎记录
            for trade in forced_redeems:
                # 获取强赎详细信息
                redeem_info = _get_forced_redeem_info(trade['ts_code'], trade['date'])
                cost_basis = _get_cost_basis(trade['ts_code'], trades, trade['date'])

                if cost_basis > 0:
                    return_pct = (trade['price'] - cost_basis) / cost_basis * 100
                    profit_loss = "盈利" if return_pct > 0 else "亏损"
                    print(f"  强赎 {trade['ts_code']} {trade['quantity']}张 @{trade['price']:.3f}元 ({profit_loss}: {abs(return_pct):.1f}%) - {redeem_info}")
                else:
                    print(f"  强赎 {trade['ts_code']} {trade['quantity']}张 @{trade['price']:.3f}元 - {redeem_info}")


def _get_cost_basis(ts_code, trades, sell_date):
    """
    计算成本基础（加权平均买入价格）

    Args:
        ts_code: 债券代码
        trades: 所有交易记录
        sell_date: 卖出日期

    Returns:
        float: 加权平均成本
    """
    total_cost = 0
    total_quantity = 0

    # 按时间顺序处理交易，但排除当前卖出交易
    for trade in trades:
        if trade['ts_code'] == ts_code and trade['date'] < sell_date:
            if trade['action'] == 'buy':
                total_cost += trade['quantity'] * trade['price']
                total_quantity += trade['quantity']
            elif trade['action'] in ['sell', 'forced_sell', 'forced_redeem']:
                # 按FIFO原则减少持仓
                if total_quantity > 0:
                    avg_cost = total_cost / total_quantity if total_quantity > 0 else 0
                    sold_cost = trade['quantity'] * avg_cost
                    total_cost -= sold_cost
                    total_quantity -= trade['quantity']
        elif trade['ts_code'] == ts_code and trade['date'] == sell_date and trade['action'] == 'buy':
            # 同一天的买入交易也要计算在内
            total_cost += trade['quantity'] * trade['price']
            total_quantity += trade['quantity']

    return total_cost / total_quantity if total_quantity > 0 else 0


def _get_forced_redeem_info(ts_code, redeem_date):
    """
    获取强制赎回的详细信息

    Args:
        ts_code: 债券代码
        redeem_date: 赎回日期

    Returns:
        str: 强赎信息描述
    """
    try:
        import sqlite3

        conn = sqlite3.connect(config_manager.get_db_path())

        # 获取强赎信息
        redeem_date_db = redeem_date.replace('-', '')
        call_query = """
        SELECT call_price, call_type
        FROM cb_call
        WHERE ts_code = ? AND call_date = ?
        """
        call_result = conn.execute(call_query, (ts_code, redeem_date_db)).fetchone()

        # 获取当日正股价格和转股价
        stock_query = """
        SELECT cb.conv_price, s.close as stock_price
        FROM cb_basic cb
        LEFT JOIN stock_daily s ON cb.stk_code = s.ts_code AND s.trade_date = ?
        WHERE cb.ts_code = ?
        """
        stock_result = conn.execute(stock_query, (redeem_date_db, ts_code)).fetchone()

        conn.close()

        if call_result and stock_result:
            call_price = call_result[0] if call_result[0] else 0
            conv_price = stock_result[0] if stock_result[0] else 0
            stock_price = stock_result[1] if stock_result[1] else 0

            if conv_price > 0 and stock_price > 0:
                conv_value = (100.0 / conv_price) * stock_price
                if conv_value > call_price:
                    return f"转股价值{conv_value:.3f}元 > 强赎价{call_price:.3f}元，执行转股"
                else:
                    return f"转股价值{conv_value:.3f}元 < 强赎价{call_price:.3f}元，接受强赎"
            else:
                return f"强赎价{call_price:.3f}元"

        return "强制赎回"

    except Exception as e:
        return f"强制赎回 (信息获取失败: {e})"





def get_all_available_factors():
    """
    获取所有可用的因子列表

    Returns:
        所有可用因子的列表
    """
    from factor.autogluon_predictor import AutoGluonFactorPredictor
    from factor.factor_calculator_factory import FactorCalculatorFactory

    # 创建预测器实例来获取完整的因子配置
    predictor = AutoGluonFactorPredictor()

    # 合并所有因子配置
    all_factors = []

    # 从默认特征配置获取因子
    for category, factors in predictor.default_feature_config.items():
        all_factors.extend(factors)

    # 从完整特征配置获取因子（去重）
    for category, factors in predictor.full_feature_config.items():
        for factor in factors:
            if factor not in all_factors:
                all_factors.append(factor)

    # 添加一些基础因子（可能不在上述配置中）
    basic_factors = [
        'ts_code', 'trade_date', 'close', 'conv_price', 'conv_value',
        'zl_price', 'combined_factor', 'stock_close'
    ]

    for factor in basic_factors:
        if factor not in all_factors:
            all_factors.append(factor)

    return all_factors


def get_factor_data_with_all_factors(start_date_db, end_date_db, conn):
    """
    获取包含所有可用因子的数据

    Args:
        start_date_db: 开始日期（YYYYMMDD格式）
        end_date_db: 结束日期（YYYYMMDD格式）
        conn: 数据库连接

    Returns:
        tuple: (factor_data, available_factors)
    """
    # 获取所有可用的因子
    available_factors = get_available_factors_from_db(conn, start_date_db, end_date_db)

    if not available_factors:
        return None, []

    # 构建动态SQL查询
    factor_columns = ', '.join(available_factors)
    factor_query = f"""
    SELECT ts_code, trade_date, {factor_columns}
    FROM cb_factor
    WHERE trade_date >= '{start_date_db}' AND trade_date <= '{end_date_db}'
    ORDER BY trade_date, ts_code
    """

    factor_data = pd.read_sql(factor_query, conn)

    return factor_data, available_factors


def get_available_factors_from_db(conn, start_date_db, end_date_db):
    """
    从数据库获取实际可用的因子列表

    Args:
        conn: 数据库连接
        start_date_db: 开始日期（YYYYMMDD格式）
        end_date_db: 结束日期（YYYYMMDD格式）

    Returns:
        实际可用的因子列表
    """
    # 获取cb_factor表的所有列名
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(cb_factor)")
    columns_info = cursor.fetchall()
    db_columns = [col[1] for col in columns_info]

    # 获取理论上所有可用的因子
    all_factors = get_all_available_factors()

    # 筛选出数据库中实际存在的因子
    available_factors = [factor for factor in all_factors if factor in db_columns]

    # 检查这些因子在指定时间段内是否有数据
    factors_with_data = []
    for factor in available_factors:
        if factor in ['ts_code', 'trade_date']:  # 跳过标识列
            continue

        try:
            query = f"""
            SELECT COUNT(*) as cnt
            FROM cb_factor
            WHERE trade_date >= '{start_date_db}' AND trade_date <= '{end_date_db}'
            AND {factor} IS NOT NULL
            """
            result = pd.read_sql(query, conn)
            if result['cnt'].iloc[0] > 0:
                factors_with_data.append(factor)
        except Exception as e:
            print(f"检查因子 {factor} 时出错: {e}")
            continue

    return factors_with_data


def run_ic_mode(args):
    """
    运行IC值计算模式

    Args:
        args: 命令行参数
    """
    import numpy as np
    import sqlite3
    from scipy.stats import pearsonr

    print("📊 因子IC值计算模式")

    # 解析日期参数
    start_date = args.start_date or '2024-01-01'
    end_date = args.end_date or '2024-12-31'

    print(f"计算时间段: {start_date} 到 {end_date}")

    # 转换日期格式为YYYYMMDD
    start_date_db = start_date.replace('-', '')
    end_date_db = end_date.replace('-', '')

    # 连接数据库
    conn = sqlite3.connect(config_manager.get_db_path())

    try:
        # 获取所有可用的因子数据
        print("🔍 检测数据库中可用的因子...")
        factor_data, available_factors = get_factor_data_with_all_factors(start_date_db, end_date_db, conn)

        if factor_data is None or not available_factors:
            print("❌ 没有找到可用的因子数据")
            return

        print(f"✅ 找到 {len(available_factors)} 个可用因子:")
        for i, factor in enumerate(available_factors, 1):
            print(f"  {i:2d}. {factor}")
        
        if factor_data.empty:
            print("❌ 没有找到因子数据")
            return
        
        print(f"📊 加载因子数据: {len(factor_data)} 条记录")
        
        # 获取价格数据计算收益率
        price_query = f"""
        SELECT ts_code, trade_date, close
        FROM cb_daily 
        WHERE trade_date >= '{start_date_db}' AND trade_date <= '{end_date_db}'
        AND close > 0
        ORDER BY trade_date, ts_code
        """
        
        price_data = pd.read_sql(price_query, conn)
        
        if price_data.empty:
            print("❌ 没有找到价格数据")
            return
        
        print(f"📊 加载价格数据: {len(price_data)} 条记录")
        
        # 计算未来收益率
        price_data = price_data.sort_values(['ts_code', 'trade_date'])
        price_data['next_return'] = price_data.groupby('ts_code')['close'].pct_change(periods=-1) * -1  # 获取下一期收益率
        
        # 合并因子数据和收益率数据
        merged_data = pd.merge(factor_data, price_data[['ts_code', 'trade_date', 'next_return']], 
                              on=['ts_code', 'trade_date'], how='inner')
        
        # 移除空值
        merged_data = merged_data.dropna()
        
        if merged_data.empty:
            print("❌ 合并后没有有效数据")
            return
        
        print(f"📊 合并数据: {len(merged_data)} 条有效记录")
        
        # 使用之前动态获取的因子列表，过滤出在合并数据中存在的因子
        available_factors_in_data = [col for col in available_factors if col in merged_data.columns]
        
        print(f"\n📈 计算 {len(available_factors_in_data)} 个因子的IC值:")
        print("=" * 80)

        ic_results = []

        for factor in available_factors_in_data:
            factor_data_clean = merged_data[[factor, 'next_return']].dropna()
            
            if len(factor_data_clean) < 10:  # 至少需要10个观测值
                print(f"  {factor:<20}: 数据不足 ({len(factor_data_clean)} 个观测值)")
                continue
            
            # 计算IC值 (相关系数)
            try:
                ic_value, p_value = pearsonr(factor_data_clean[factor], factor_data_clean['next_return'])
                
                # 检查是否为NaN
                if np.isnan(ic_value):
                    print(f"  {factor:<20}: IC计算结果为NaN，跳过")
                    continue
                    
            except Exception as e:
                print(f"  {factor:<20}: IC计算失败 ({e})")
                continue
            
            # 计算绝对IC值 (取绝对值)
            abs_ic = abs(ic_value)
            
            # 评估IC值的显著性
            if p_value < 0.01:
                significance = "***"
            elif p_value < 0.05:
                significance = "**"
            elif p_value < 0.1:
                significance = "*"
            else:
                significance = ""
            
            # 评估IC值的强度
            if abs_ic >= 0.1:
                strength = "强"
            elif abs_ic >= 0.05:
                strength = "中"
            elif abs_ic >= 0.02:
                strength = "弱"
            else:
                strength = "极弱"
            
            ic_results.append({
                'factor': factor,
                'ic': ic_value,
                'abs_ic': abs_ic,
                'p_value': p_value,
                'significance': significance,
                'strength': strength,
                'sample_size': len(factor_data_clean)
            })
            
            # 显示结果
            direction = "正向" if ic_value > 0 else "负向"
            print(f"  {factor:<20}: IC={ic_value:>7.4f} {significance:<3} | "
                  f"绝对IC={abs_ic:>6.4f} | {strength:<2} | {direction} | "
                  f"样本={len(factor_data_clean):>5}个")
        
        if not ic_results:
            print("❌ 没有计算出任何IC值")
            return
        
        # 按绝对IC值排序
        ic_results.sort(key=lambda x: x['abs_ic'], reverse=True)
        
        print("\n" + "=" * 80)
        print("📊 IC值分析总结:")
        print("=" * 80)
        
        print(f"\n🏆 Top 5 因子 (按绝对IC值排序):")
        for i, result in enumerate(ic_results[:5], 1):
            direction = "正向" if result['ic'] > 0 else "负向"
            print(f"  {i}. {result['factor']:<20}: 绝对IC={result['abs_ic']:.4f} ({result['strength']}) {direction}")
        
        # 统计分析
        strong_factors = [r for r in ic_results if r['abs_ic'] >= 0.1]
        medium_factors = [r for r in ic_results if 0.05 <= r['abs_ic'] < 0.1]
        weak_factors = [r for r in ic_results if 0.02 <= r['abs_ic'] < 0.05]
        very_weak_factors = [r for r in ic_results if r['abs_ic'] < 0.02]
        
        print(f"\n📈 因子强度分布:")
        print(f"  强因子 (|IC|≥0.10): {len(strong_factors)} 个")
        print(f"  中等因子 (0.05≤|IC|<0.10): {len(medium_factors)} 个")
        print(f"  弱因子 (0.02≤|IC|<0.05): {len(weak_factors)} 个")
        print(f"  极弱因子 (|IC|<0.02): {len(very_weak_factors)} 个")
        
        # 显著性分析
        significant_001 = [r for r in ic_results if r['p_value'] < 0.01]
        significant_005 = [r for r in ic_results if 0.01 <= r['p_value'] < 0.05]
        significant_010 = [r for r in ic_results if 0.05 <= r['p_value'] < 0.1]
        
        print(f"\n📊 统计显著性:")
        print(f"  高度显著 (p<0.01): {len(significant_001)} 个")
        print(f"  显著 (0.01≤p<0.05): {len(significant_005)} 个")
        print(f"  边际显著 (0.05≤p<0.1): {len(significant_010)} 个")
        
        # 建议
        print(f"\n💡 投资建议:")
        if strong_factors:
            print(f"  ✅ 推荐使用强因子: {', '.join([f['factor'] for f in strong_factors])}")
        elif medium_factors:
            print(f"  ⚠️  可考虑中等因子: {', '.join([f['factor'] for f in medium_factors])}")
        else:
            print(f"  ❌ 当前时间段内所有因子效果都较弱，建议调整策略或时间段")
        
        # 计算平均IC
        if ic_results:
            avg_ic = np.mean([r['abs_ic'] for r in ic_results])
            print(f"\n📊 整体因子质量: 平均绝对IC = {avg_ic:.4f}")
            
            if avg_ic >= 0.05:
                print("  🎉 因子质量良好")
            elif avg_ic >= 0.03:
                print("  ⚠️  因子质量一般")
            else:
                print("  ❌ 因子质量较差，需要优化")
        else:
            print("\n📊 整体因子质量: 无有效IC值计算结果")
        
    except Exception as e:
        print(f"❌ IC计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()


def _parse_train_interval(train_interval):
    """
    解析训练间隔字符串

    Args:
        train_interval: 训练间隔字符串，如'3M', '6M', '1Y'

    Returns:
        tuple: (间隔数值, 间隔单位)
    """
    train_interval = train_interval.upper()

    if train_interval.endswith('M'):
        return int(train_interval[:-1]), 'months'
    elif train_interval.endswith('Y'):
        return int(train_interval[:-1]), 'years'
    else:
        # 默认按月处理
        try:
            return int(train_interval), 'months'
        except ValueError:
            print(f"⚠️ 无法解析训练间隔 '{train_interval}'，使用默认值3个月")
            return 3, 'months'


def _generate_flow_periods(backtest_start_date, backtest_end_date, interval_value, interval_unit, train_start_date=None):
    """
    生成流式回测的训练和测试时间段

    Args:
        backtest_start_date: 回测开始日期字符串 'YYYY-MM-DD'
        backtest_end_date: 回测结束日期字符串 'YYYY-MM-DD'
        interval_value: 间隔数值
        interval_unit: 间隔单位 ('months' 或 'years')
        train_start_date: 训练数据起始日期字符串 'YYYY-MM-DD'，如果为None则使用backtest_start_date

    Returns:
        list: 包含训练和测试时间段的字典列表
    """
    from datetime import datetime
    import dateutil.relativedelta as rd

    periods = []

    # 转换为datetime对象
    backtest_start_dt = datetime.strptime(backtest_start_date, '%Y-%m-%d')
    backtest_end_dt = datetime.strptime(backtest_end_date, '%Y-%m-%d')

    # 训练数据起始日期，如果未指定则使用回测开始日期
    if train_start_date:
        train_start_dt = datetime.strptime(train_start_date, '%Y-%m-%d')
    else:
        train_start_dt = backtest_start_dt

    # 计算时间增量
    if interval_unit == 'months':
        delta = rd.relativedelta(months=interval_value)
    else:  # years
        delta = rd.relativedelta(years=interval_value)

    # 当前回测期的开始时间
    current_test_start = backtest_start_dt

    while current_test_start < backtest_end_dt:
        # 当前回测期的结束时间
        current_test_end = min(current_test_start + delta, backtest_end_dt)

        # 训练期结束时间：回测期开始前一天
        train_end_dt = current_test_start - rd.relativedelta(days=1)

        # 确保训练期至少有6个月的数据
        min_train_dt = train_start_dt + rd.relativedelta(months=6)
        if train_end_dt >= min_train_dt:
            # 确保测试期至少有一个月的数据
            if (current_test_end - current_test_start).days >= 30:
                periods.append({
                    'train_start': train_start_dt.strftime('%Y-%m-%d'),
                    'train_end': train_end_dt.strftime('%Y-%m-%d'),
                    'test_start': current_test_start.strftime('%Y-%m-%d'),
                    'test_end': current_test_end.strftime('%Y-%m-%d')
                })

        # 下一个回测期的开始时间
        current_test_start = current_test_end

    return periods


def _create_backtest_args(original_args, period, model_name, rebalance_frequency, use_ml_model=False, use_linear_model=False, use_ft_model=False):
    """
    创建用于单次回测的参数对象

    Args:
        original_args: 原始命令行参数
        period: 时间段字典
        model_name: 模型名称
        rebalance_frequency: 调整频率
        use_ml_model: 是否使用ML模型
        use_linear_model: 是否使用线性模型
        use_ft_model: 是否使用FT-Transformer模型

    Returns:
        参数对象
    """
    import argparse

    # 创建新的参数对象
    backtest_args = argparse.Namespace()

    # 复制原始参数
    for key, value in vars(original_args).items():
        setattr(backtest_args, key, value)

    # 设置回测特定参数
    backtest_args.start_date = period['test_start']
    backtest_args.end_date = period['test_end']
    backtest_args.rebalance_frequency = rebalance_frequency
    
    # 设置模型参数
    if use_ml_model:
        backtest_args.use_ml_model = True
        backtest_args.ml_model_name = model_name
        backtest_args.use_linear_model = False
        backtest_args.use_ft_model = False
    elif use_linear_model:
        backtest_args.use_linear_model = True
        backtest_args.model_name = model_name
        backtest_args.use_ml_model = False
        backtest_args.use_ft_model = False
    elif use_ft_model:
        backtest_args.use_ft_model = True
        backtest_args.ft_model_name = model_name
        backtest_args.use_ml_model = False
        backtest_args.use_linear_model = False
    else:
        # 默认使用ML模型以保持向后兼容
        backtest_args.use_ml_model = True
        backtest_args.ml_model_name = model_name
        backtest_args.use_linear_model = False
        backtest_args.use_ft_model = False

    # 设置默认策略参数
    if not hasattr(backtest_args, 'strategy') or not backtest_args.strategy:
        backtest_args.strategy = 'topndropoutk'
    if not hasattr(backtest_args, 'params') or not backtest_args.params:
        backtest_args.params = 'n=10,k=2'

    return backtest_args


def run_flow_backtest_mode(args):
    """
    运行流式回测模式

    Args:
        args: 命令行参数
    """
    from datetime import datetime, timedelta
    import dateutil.relativedelta as rd
    from factor.autogluon_predictor import train_autogluon_predictor
    from factor.linear_regression_optimizer import train_factor_regression
    from factor.ft_transformer_predictor import train_ft_transformer_predictor

    print("🌊 流式回测模式")
    print("=" * 80)

    # 解析参数
    start_date = args.start_date or '2020-01-01'
    end_date = args.end_date or '2024-12-31'
    train_start_date = args.train_start_date
    model_prefix = args.model_prefix or 'flow_model'
    train_interval = args.train_interval or '3M'
    presets = getattr(args, 'presets', 'high_quality')
    target_days = getattr(args, 'target_days', 10)
    time_limit = getattr(args, 'time_limit', 600)
    rebalance_frequency = getattr(args, 'rebalance_frequency', 'weekly')
    
    # 检查模型类型
    use_ml_model = getattr(args, 'use_ml_model', False)
    use_linear_model = getattr(args, 'use_linear_model', False)
    
    # 检查是否为FT-Transformer模式
    is_ft_transformer_mode = getattr(args, 'mode', None) == 'ft_transformer'
    use_ft_model = is_ft_transformer_mode and use_ml_model
    
    if not use_ml_model and not use_linear_model:
        print("⚠️ 未指定模型类型，默认使用ML模型")
        use_ml_model = True

    print(f"回测时间段: {start_date} - {end_date}")
    if train_start_date:
        print(f"训练数据起始日期: {train_start_date}")
    print(f"模型前缀: {model_prefix}")
    print(f"训练间隔: {train_interval}")
    if use_ml_model:
        print(f"模型类型: AutoGluon ML模型")
        print(f"模型预设: {presets}")
        print(f"训练时间限制: {time_limit}秒")
    elif use_linear_model:
        print(f"模型类型: 线性回归模型")
        regularization = getattr(args, 'regularization', 'ridge')
        reg_alpha = getattr(args, 'reg_alpha', 1.0)
        factor_config = getattr(args, 'factor_config', 'default')
        print(f"正则化方法: {regularization}")
        print(f"正则化强度: {reg_alpha}")
        print(f"因子配置: {factor_config}")
    elif use_ft_model:
        print(f"模型类型: FT-Transformer深度学习模型")
        print(f"训练时间限制: {time_limit}秒")
    print(f"目标天数: {target_days}")
    print(f"调整频率: {rebalance_frequency}")

    # 解析训练间隔
    interval_value, interval_unit = _parse_train_interval(train_interval)

    # 生成训练和回测时间段
    periods = _generate_flow_periods(start_date, end_date, interval_value, interval_unit, train_start_date)

    if not periods:
        print("❌ 无法生成有效的训练和回测时间段")
        return

    print(f"\n📅 生成了 {len(periods)} 个训练-回测周期:")
    for i, period in enumerate(periods, 1):
        print(f"  周期 {i}: 训练 {period['train_start']} - {period['train_end']}, "
              f"回测 {period['test_start']} - {period['test_end']}")

    # 执行流式回测
    all_results = []

    for i, period in enumerate(periods, 1):
        print(f"\n{'='*60}")
        print(f"🔄 执行周期 {i}/{len(periods)}")
        print(f"{'='*60}")

        # 生成模型名称
        model_name = f"{model_prefix}_{period['train_end'].replace('-', '')}"

        # 第一步：训练模型
        print(f"\n[训练] 步骤1: 训练模型 {model_name}")
        print(f"训练数据: {period['train_start']} - {period['train_end']}")

        # 检查是否有足够的因子数据进行训练
        if not _check_factor_data_availability(period['train_start'], period['train_end']):
            print(f"⚠️ 训练期间因子数据不足，跳过模型训练")
            print(f"💡 建议先运行: python main.py --mode factor --start_date {period['train_start']} --end_date {period['train_end']}")
            continue

        try:
            if use_ml_model:
                # 训练AutoGluon ML模型
                predictor = train_autogluon_predictor(
                    train_start=period['train_start'].replace('-', ''),
                    train_end=period['train_end'].replace('-', ''),
                    model_name=model_name,
                    target_days=target_days,
                    time_limit=time_limit,
                    presets=presets,
                    preprocessing_config=getattr(args, 'preprocessing_config', 'no_imputation'),
                    use_sample_weights=getattr(args, 'use_sample_weights', False),
                    sorted_feature=getattr(args, 'sorted_feature', False)
                )

                if not predictor:
                    print(f"❌ AutoGluon模型 {model_name} 训练失败，跳过此周期")
                    continue

                print(f"✅ AutoGluon模型 {model_name} 训练完成")
                
            elif use_linear_model:
                # 训练线性回归模型
                regularization = getattr(args, 'regularization', 'ridge')
                reg_alpha = getattr(args, 'reg_alpha', 1.0)
                factor_config_name = getattr(args, 'factor_config', 'default')
                target_factor = getattr(args, 'target_factor', 'next_return')
                
                linear_model = train_factor_regression(
                    train_start=period['train_start'].replace('-', ''),
                    train_end=period['train_end'].replace('-', ''),
                    regularization=regularization,
                    alpha=reg_alpha,
                    model_name=model_name,
                    factor_config=None,  # 使用预设配置
                    target_factor=target_factor,
                    return_days=target_days,
                    custom_factors=getattr(args, 'custom_factors', None),
                    save_model=True,
                    factor_config_name=factor_config_name,
                    use_sample_weights=getattr(args, 'use_sample_weights', False),
                    sorted_feature=getattr(args, 'sorted_feature', False)
                )

                if not linear_model:
                    print(f"❌ 线性回归模型 {model_name} 训练失败，跳过此周期")
                    continue

                print(f"✅ 线性回归模型 {model_name} 训练完成")
                
            elif use_ft_model:
                # 训练FT-Transformer模型
                ft_predictor = train_ft_transformer_predictor(
                    train_start=period['train_start'].replace('-', ''),
                    train_end=period['train_end'].replace('-', ''),
                    model_name=model_name,
                    target_days=target_days,
                    time_limit=time_limit,
                    custom_features=getattr(args, 'custom_factors', None),
                    preprocessing_config=getattr(args, 'preprocessing_config', 'conservative'),
                    tuning_ratio=0.1,
                    use_sample_weights=getattr(args, 'use_sample_weights', False),
                    sorted_feature=getattr(args, 'sorted_feature', False)
                )

                if not ft_predictor:
                    print(f"❌ FT-Transformer模型 {model_name} 训练失败，跳过此周期")
                    continue

                print(f"✅ FT-Transformer模型 {model_name} 训练完成")

        except Exception as e:
            print(f"❌ 模型 {model_name} 训练异常: {e}")
            continue

        # 第二步：使用模型进行回测
        print(f"\n📊 步骤2: 回测")
        print(f"回测数据: {period['test_start']} - {period['test_end']}")

        # 创建临时args对象用于回测
        backtest_args = _create_backtest_args(args, period, model_name, rebalance_frequency, use_ml_model, use_linear_model, use_ft_model)

        # 执行回测
        backtest_result = _execute_single_backtest(backtest_args)

        if backtest_result:
            backtest_result['period'] = i
            backtest_result['model_name'] = model_name
            backtest_result['train_period'] = f"{period['train_start']} - {period['train_end']}"
            backtest_result['test_period'] = f"{period['test_start']} - {period['test_end']}"
            all_results.append(backtest_result)

            # 显示当前周期结果
            if 'performance' in backtest_result:
                perf = backtest_result['performance']
                print(f"✅ 周期 {i} 回测完成:")
                print(f"  总收益率: {perf.get('total_return', 0):.2%}")
                print(f"  年化收益率: {perf.get('annual_return', 0):.2%}")
                print(f"  最大回撤: {perf.get('max_drawdown', 0):.2%}")
                print(f"  夏普比率: {perf.get('sharpe_ratio', 0):.2f}")
        else:
            print(f"❌ 周期 {i} 回测失败")

    # 汇总结果
    if all_results:
        print(f"\n{'='*80}")
        print(f"🎯 流式回测完成 - 总结报告")
        print(f"{'='*80}")

        _print_flow_backtest_summary(all_results)
    else:
        print("\n❌ 流式回测失败，没有获得任何有效结果")


def _execute_single_backtest(backtest_args):
    """
    执行单次回测

    Args:
        backtest_args: 回测参数对象

    Returns:
        回测结果字典或None
    """
    try:
        # 解析策略参数
        params = parse_params(backtest_args.params)

        # 创建策略
        if backtest_args.strategy == 'topndropoutk':
            n = params.get('n', 10)
            k = params.get('k', 2)
            d = params.get('d', 40)
            strategy = TopNDropoutKStrategy(n=n, k=k, d=d)
        else:
            print(f"❌ 不支持的策略: {backtest_args.strategy}")
            return None

        # 创建筛选器
        filter_type = getattr(backtest_args, 'filter', 'none')
        try:
            bond_filter = create_filter_from_args(filter_type)
        except ValueError as e:
            print(f"❌ 筛选器创建失败: {e}")
            return None

        # 创建回测器
        backtester = Backtester(
            start_date=backtest_args.start_date,
            end_date=backtest_args.end_date,
            strategy=strategy,
            initial_capital=config_manager.get_default_initial_capital(),
            precision_level=getattr(backtest_args, 'precision', 'medium'),
            rebalance_frequency=backtest_args.rebalance_frequency
        )

        # 设置筛选器
        backtester.set_filter(bond_filter)

        # 设置模型
        if hasattr(backtest_args, 'use_ml_model') and backtest_args.use_ml_model:
            backtester.set_ml_model(backtest_args.ml_model_name)
        elif hasattr(backtest_args, 'use_linear_model') and backtest_args.use_linear_model:
            factor_config_name = getattr(backtest_args, 'factor_config', 'default')
            backtester.set_linear_model(backtest_args.model_name, factor_config_name)

        # 运行回测
        factor_name = getattr(backtest_args, 'factor_name', 'combined_factor')
        factor_ascending = getattr(backtest_args, 'factor_ascending', False)

        results = backtester.run(factor_name=factor_name, ascending=factor_ascending)

        return results

    except Exception as e:
        print(f"❌ 回测执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def _check_factor_data_availability(start_date, end_date):
    """
    检查指定时间段内是否有足够的因子数据用于训练

    Args:
        start_date: 开始日期 'YYYY-MM-DD'
        end_date: 结束日期 'YYYY-MM-DD'

    Returns:
        bool: 是否有足够的因子数据
    """
    import sqlite3

    try:
        conn = sqlite3.connect(config_manager.get_db_path())

        # 转换日期格式
        start_date_str = start_date.replace('-', '')
        end_date_str = end_date.replace('-', '')

        # 检查关键因子的数据量
        query = """
        SELECT COUNT(*) as total_count,
               COUNT(CASE WHEN arbitrage_space IS NOT NULL AND arbitrage_space != '' THEN 1 END) as arbitrage_count,
               COUNT(CASE WHEN momentum_5d IS NOT NULL AND momentum_5d != '' THEN 1 END) as momentum_count,
               COUNT(CASE WHEN combined_factor IS NOT NULL AND combined_factor != '' THEN 1 END) as combined_count
        FROM cb_factor
        WHERE trade_date >= ? AND trade_date <= ?
        """

        result = pd.read_sql(query, conn, params=[start_date_str, end_date_str])
        conn.close()

        if result.empty:
            return False

        total_count = result['total_count'].iloc[0]
        arbitrage_count = result['arbitrage_count'].iloc[0]
        momentum_count = result['momentum_count'].iloc[0]
        combined_count = result['combined_count'].iloc[0]

        print(f"📊 因子数据检查 ({start_date} - {end_date}):")
        print(f"  总记录数: {total_count}")
        print(f"  套利空间因子: {arbitrage_count} ({arbitrage_count/total_count*100:.1f}%)" if total_count > 0 else "  套利空间因子: 0")
        print(f"  动量因子: {momentum_count} ({momentum_count/total_count*100:.1f}%)" if total_count > 0 else "  动量因子: 0")
        print(f"  组合因子: {combined_count} ({combined_count/total_count*100:.1f}%)" if total_count > 0 else "  组合因子: 0")

        # 至少需要有组合因子数据，或者有一定比例的其他因子数据
        if combined_count > 0:
            return True
        elif total_count > 0 and (arbitrage_count + momentum_count) / total_count > 0.1:
            return True
        else:
            return False

    except Exception as e:
        print(f"❌ 检查因子数据时出错: {e}")
        return False


def _print_flow_backtest_summary(all_results):
    """
    打印流式回测汇总报告

    Args:
        all_results: 所有回测结果列表
    """
    if not all_results:
        return

    # 收集所有周期的数据
    all_ic_1d_values = []
    all_ic_5d_values = []
    all_ic_10d_values = []
    all_ic_1d_samples = []
    all_ic_5d_samples = []
    all_ic_10d_samples = []
    
    # 收集性能数据
    total_returns = []
    annual_returns = []
    max_drawdowns = []
    sharpe_ratios = []
    volatilities = []
    total_trades_list = []
    win_rates = []
    
    # 收集所有周期的原始数据用于计算整体指标
    all_daily_values = []
    all_trades = []
    
    for result in all_results:
        if 'performance' in result:
            perf = result['performance']
            
            # 收集性能指标
            total_returns.append(perf.get('total_return', 0))
            annual_returns.append(perf.get('annual_return', 0))
            max_drawdowns.append(perf.get('max_drawdown', 0))
            sharpe_ratios.append(perf.get('sharpe_ratio', 0))
            volatilities.append(perf.get('volatility', 0))
            total_trades_list.append(perf.get('total_trades', 0))
            win_rates.append(perf.get('win_rate', 0))
            
        # 收集IC统计数据
        if 'ic_stats' in result:
            ic_stats = result['ic_stats']
            
            if 'ic_1d' in ic_stats:
                all_ic_1d_values.append(ic_stats['ic_1d'])
                # 从原始数据估算样本数（每个周期大约有相同的样本数）
                sample_count = len(result.get('results', {}).get('daily_values', [])) * 20  # 估算
                all_ic_1d_samples.append(sample_count)
                
            if 'ic_5d' in ic_stats:
                all_ic_5d_values.append(ic_stats['ic_5d'])
                sample_count = len(result.get('results', {}).get('daily_values', [])) * 15  # 估算
                all_ic_5d_samples.append(sample_count)
                
            if 'ic_10d' in ic_stats:
                all_ic_10d_values.append(ic_stats['ic_10d'])
                sample_count = len(result.get('results', {}).get('daily_values', [])) * 10  # 估算
                all_ic_10d_samples.append(sample_count)
        
        # 收集原始数据
        if 'results' in result and 'daily_values' in result['results']:
            all_daily_values.extend(result['results']['daily_values'])
            
        if 'results' in result and 'trades' in result['results']:
            all_trades.extend(result['results']['trades'])
    
    # 计算整体IC统计
    print(f"\n📊 整体IC统计:")
    
    if all_ic_1d_values:
        # 使用样本数加权平均计算整体IC
        total_1d_samples = sum(all_ic_1d_samples)
        weighted_ic_1d = sum(ic * samples for ic, samples in zip(all_ic_1d_values, all_ic_1d_samples)) / total_1d_samples if total_1d_samples > 0 else 0
        print(f"1日IC值: {weighted_ic_1d:.4f} (样本数: {total_1d_samples})")
    
    if all_ic_5d_values:
        total_5d_samples = sum(all_ic_5d_samples)
        weighted_ic_5d = sum(ic * samples for ic, samples in zip(all_ic_5d_values, all_ic_5d_samples)) / total_5d_samples if total_5d_samples > 0 else 0
        print(f"5日IC值: {weighted_ic_5d:.4f} (样本数: {total_5d_samples})")
    
    if all_ic_10d_values:
        total_10d_samples = sum(all_ic_10d_samples)
        weighted_ic_10d = sum(ic * samples for ic, samples in zip(all_ic_10d_values, all_ic_10d_samples)) / total_10d_samples if total_10d_samples > 0 else 0
        print(f"10日IC值: {weighted_ic_10d:.4f} (样本数: {total_10d_samples})")
    
    # 计算整体回测结果
    if total_returns:
        # 计算整体收益率（复合收益率）
        cumulative_return = 1.0
        for ret in total_returns:
            cumulative_return *= (1 + ret)
        overall_total_return = cumulative_return - 1
        
        # 计算整体年化收益率
        if all_daily_values:
            # 计算总的回测天数
            start_date = min(pd.to_datetime(dv['date']) for dv in all_daily_values)
            end_date = max(pd.to_datetime(dv['date']) for dv in all_daily_values)
            total_days = (end_date - start_date).days
            years = total_days / 365.25
            overall_annual_return = (cumulative_return ** (1/years) - 1) if years > 0 else 0
        else:
            overall_annual_return = np.mean(annual_returns)
        
        # 计算整体最大回撤（使用最大值）
        overall_max_drawdown = max(max_drawdowns) if max_drawdowns else 0
        
        # 计算整体夏普比率（加权平均）
        overall_sharpe_ratio = np.mean(sharpe_ratios) if sharpe_ratios else 0
        
        # 计算整体波动率（加权平均）
        overall_volatility = np.mean(volatilities) if volatilities else 0
        
        # 计算整体胜率
        positive_periods = sum(1 for ret in total_returns if ret > 0)
        overall_win_rate = positive_periods / len(total_returns) if total_returns else 0
        
        # 计算总交易次数
        total_trades_sum = sum(total_trades_list)
        
        print(f"\n🎯 回测结果:")
        print(f"总收益率: {overall_total_return:.2%}")
        print(f"年化收益率: {overall_annual_return:.2%}")
        print(f"最大回撤: {overall_max_drawdown:.2%}")
        print(f"夏普比率: {overall_sharpe_ratio:.2f}")
        print(f"年化波动率: {overall_volatility:.2%}")
        print(f"胜率: {overall_win_rate:.2%}")
        print(f"总交易次数: {total_trades_sum}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    try:
        # 根据运行模式执行相应的功能
        if args.mode == 'data':
            run_data_mode(args)
        elif args.mode == 'factor':
            run_factor_mode(args)
        elif args.mode == 'backtest':
            run_backtest_mode(args)
        elif args.mode == 'realtime':
            run_realtime_mode(args)
        elif args.mode == 'optimize':
            run_optimize_mode(args)
        elif args.mode == 'status':
            run_status_mode(args)

        elif args.mode == 'data_check':
            run_data_check_mode(args)
        elif args.mode == 'linear_model':
            run_linear_model_mode(args)
        elif args.mode == 'ml_model':
            run_ml_model_mode(args)
        elif args.mode == 'ft_transformer':
            run_ft_transformer_model_mode(args)
        elif args.mode == 'incremental_factor':
            run_incremental_factor_mode(args)
        elif args.mode == 'ic' or args.ic:
            run_ic_mode(args)
        elif args.mode == 'flow_backtest':
            run_flow_backtest_mode(args)
        else:
            # 如果未指定模式，显示帮助信息
            print("请指定运行模式，使用 --help 查看帮助信息")
    
    except Exception as e:
        # 处理异常
        error_handler.handle_error(e)


if __name__ == '__main__':
    main()
